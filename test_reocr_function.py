#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重新OCR功能的脚本
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils import ConfigLoader
from core import TaskStatusMonitor, TAPDService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_reocr_function():
    """测试重新OCR功能"""
    print("🧪 开始测试重新OCR功能")
    
    # 初始化服务
    config_loader = ConfigLoader()
    task_monitor = TaskStatusMonitor(config_loader)
    tapd_service = TAPDService(config_loader)
    
    print("✅ 服务初始化完成")
    
    # 获取所有需求，查找有后续任务的需求
    print("🔍 查找有后续任务的需求...")
    
    # 获取所有负责人的任务
    responsible_persons = config_loader.get_responsible_persons()
    all_tasks = []
    
    for person in responsible_persons.values():
        tasks = await tapd_service.get_person_tasks(person)
        all_tasks.extend(tasks)
    
    print(f"📋 共获取到 {len(all_tasks)} 个任务")
    
    # 按需求ID分组任务
    story_tasks = {}
    for task in all_tasks:
        story_id = task.get('story_id')
        if story_id:
            if story_id not in story_tasks:
                story_tasks[story_id] = []
            story_tasks[story_id].append(task)
    
    print(f"📊 共有 {len(story_tasks)} 个需求")
    
    # 查找有后续任务的需求
    followup_rules = config_loader.get_ocr_config().get("followup_rules", [])
    followup_task_titles = [rule.get('new_task_title', '') for rule in followup_rules]
    
    print(f"🔧 配置的后续任务类型: {followup_task_titles}")
    
    candidates = []
    for story_id, tasks in story_tasks.items():
        has_original = False
        has_followup = False
        has_reocr = False
        
        for task in tasks:
            task_name = task.get('name', '')
            
            # 检查原始任务
            if ('准确性问题' in task_name or '完备性问题' in task_name or 
                task_name.startswith('准确性问题-') or task_name.startswith('完备性问题-')):
                has_original = True
            
            # 检查后续任务
            if any(title in task_name for title in followup_task_titles if title):
                has_followup = True
            
            # 检查重新OCR任务
            if '重新OCR' in task_name:
                has_reocr = True
        
        if has_original and has_followup and not has_reocr:
            candidates.append({
                'story_id': story_id,
                'task_count': len(tasks),
                'tasks': tasks
            })
    
    print(f"🎯 找到 {len(candidates)} 个候选需求（有原始任务和后续任务，但没有重新OCR任务）")

    # 显示所有需求的详细信息
    print(f"\n📋 所有需求详细信息:")
    for story_id, tasks in story_tasks.items():
        print(f"\n需求 {story_id}:")
        has_original = False
        has_followup = False
        has_reocr = False

        for task in tasks:
            task_name = task.get('name', '')
            task_status = task.get('status', '')
            print(f"  - {task_name} (状态: {task_status})")

            # 检查任务类型
            if ('准确性问题' in task_name or '完备性问题' in task_name or
                task_name.startswith('准确性问题-') or task_name.startswith('完备性问题-')):
                has_original = True
                print(f"    ✓ 原始任务")

            if any(title in task_name for title in followup_task_titles if title):
                has_followup = True
                print(f"    ✓ 后续任务")

            if '重新OCR' in task_name:
                has_reocr = True
                print(f"    ✓ 重新OCR任务")

        print(f"  状态: 原始任务={has_original}, 后续任务={has_followup}, 重新OCR={has_reocr}")

    if not candidates:
        print("\n❌ 没有找到合适的测试需求")

        # 尝试手动测试一个需求
        if story_tasks:
            test_story_id = list(story_tasks.keys())[0]
            print(f"\n🧪 手动测试需求: {test_story_id}")
            await task_monitor._check_single_batch_completion(test_story_id)

        return
    
    # 选择第一个候选需求进行测试
    test_story = candidates[0]
    story_id = test_story['story_id']
    
    print(f"\n🧪 测试需求: {story_id}")
    print(f"📝 任务列表:")
    for task in test_story['tasks']:
        print(f"  - {task.get('name', '')} (状态: {task.get('status', '')})")
    
    # 手动触发批次完成检查
    print(f"\n⚡ 手动触发批次完成检查...")
    await task_monitor._check_single_batch_completion(story_id)
    
    print("✅ 测试完成")

if __name__ == '__main__':
    asyncio.run(test_reocr_function())
