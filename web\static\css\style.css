/* OCR校验流程监控看板样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
}

/* 卡片样式增强 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* 统计卡片样式 */
.card.bg-primary, .card.bg-success, .card.bg-info, .card.bg-warning, .card.bg-danger {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
}

.card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20692b 100%);
}

.card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
}

.card.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #a02834 100%);
}

/* 进度条样式 */
.progress {
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    border-radius: 10px;
    transition: width 1s ease-in-out;
}

.progress-text {
    color: white;
    font-weight: bold;
    line-height: 20px;
}

/* 工作流阶段卡片 */
.workflow-stage {
    text-align: center;
    padding: 15px;
    margin: 10px 0;
    border-radius: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.workflow-stage.completed {
    background: linear-gradient(135deg, #28a745, #20692b);
    color: white;
}

.workflow-stage.in-progress {
    background: linear-gradient(135deg, #17a2b8, #117a8b);
    color: white;
    animation: pulse 2s infinite;
}

.workflow-stage.pending {
    background: linear-gradient(135deg, #ffc107, #d39e00);
    color: black;
}

.workflow-stage.not-started {
    background: #e9ecef;
    color: #6c757d;
}

/* 脉冲动画 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 时间线样式 */
.timeline {
    position: relative;
    max-height: 400px;
    overflow-y: auto;
}

.timeline-item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: 13px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background: #dee2e6;
}

.timeline-item:last-child::after {
    display: none;
}

.timeline-item.task-completed::before {
    background: #28a745;
}

.timeline-item.task-created::before {
    background: #17a2b8;
}

.timeline-item.task-overdue::before {
    background: #dc3545;
}

.timeline-content {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.timeline-time {
    font-size: 0.85em;
    color: #6c757d;
    margin-bottom: 5px;
}

/* 任务表格样式 */
.table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 15px 10px;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 状态标签样式 */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-processing {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #b8e6ff;
}

.status-completed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-overdue {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 优先级标签样式 */
.priority-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 600;
}

.priority-high {
    background: #ff6b6b;
    color: white;
}

.priority-medium {
    background: #feca57;
    color: white;
}

.priority-low {
    background: #48dbfb;
    color: white;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20692b);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #a02834);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #d39e00);
    border: none;
    color: #212529;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .table-responsive {
        font-size: 0.9em;
    }
    
    .workflow-stage {
        margin: 5px 0;
        padding: 10px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.9em;
    }
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 6px;
    border: 2px solid #e9ecef;
    transition: border-color 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* 自定义滚动条 */
.timeline::-webkit-scrollbar {
    width: 6px;
}

.timeline::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.timeline::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.timeline::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具提示样式 */
.tooltip-inner {
    background: #333;
    color: white;
    border-radius: 4px;
    font-size: 0.85em;
}

/* 成功/错误消息样式 */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* 空状态样式 */
.empty-state {
    opacity: 0.7;
}

.empty-state .bi {
    font-size: 4rem;
}

/* 统计卡片数字动画 */
.counter {
    transition: all 0.3s ease;
}

.counter.updated {
    transform: scale(1.1);
    color: #28a745;
}

/* 页面标题样式 */
.page-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* 任务详情模态框样式 */
.min-height-40 {
    min-height: 40px;
}

.problem-tag {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 4px 8px;
    margin: 2px 4px 2px 0;
    border-radius: 12px;
    font-size: 12px;
    position: relative;
    padding-right: 20px;
}

.problem-tag.type-accuracy-defect {
    background: #dc3545;
}

.problem-tag.type-accuracy-error {
    background: #fd7e14;
}

.problem-tag.type-completeness {
    background: #ffc107;
    color: #000;
}

.problem-tag.type-system {
    background: #6f42c1;
}

.problem-tag.type-no-problem {
    background: #198754;
}

.problem-tag .remove-tag {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-weight: bold;
    opacity: 0.8;
}

.problem-tag .remove-tag:hover {
    opacity: 1;
}

.modal-footer .container-fluid {
    padding: 0;
}

.modal-footer .row {
    margin: 0;
}

.modal-footer .col-12 {
    padding: 0;
}

/* 个人高亮相关样式增强 */
.process-step.my-step {
    position: relative;
    background: rgba(255, 193, 7, 0.4) !important;
    border: 2px solid #ffc107 !important;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.4) !important;
}

.process-step.my-step::before {
    content: "⭐";
    position: absolute;
    top: -6px;
    right: -6px;
    background: #ffc107;
    color: #212529;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    font-weight: bold;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    z-index: 10;
}

/* 优化tooltip样式 */
.tooltip {
    font-size: 12px;
}

.tooltip-inner {
    max-width: 250px;
    padding: 8px 12px;
    background: rgba(33, 37, 41, 0.95);
    color: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    backdrop-filter: blur(10px);
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: rgba(33, 37, 41, 0.95);
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: rgba(33, 37, 41, 0.95);
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: rgba(33, 37, 41, 0.95);
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: rgba(33, 37, 41, 0.95);
}

/* 流程步骤交互增强 */
.process-step {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: visible;
}

.process-step:hover {
    z-index: 5;
}

.process-step.my-step:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.6), 0 4px 12px rgba(0,0,0,0.3) !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .process-step.my-step:hover {
        transform: translateY(-1px) !important;
    }
    
    .process-step.my-step::before {
        width: 14px;
        height: 14px;
        font-size: 7px;
        top: -5px;
        right: -5px;
    }
} 