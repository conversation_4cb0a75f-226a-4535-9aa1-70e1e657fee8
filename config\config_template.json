{"_注释": "这是OCR监控系统的统一配置文件模板，请根据实际情况修改以下配置", "tapd": {"_说明": "TAPD API配置 - 请在TAPD个人设置中生成API凭据", "api_user": "your_tapd_username", "api_password": "your_tapd_api_password", "workspace_id": 38183800, "_workspace_id说明": "您的TAPD工作空间ID，可在TAPD URL中找到", "_api_password说明": "TAPD API口令，不是token，请使用API密码"}, "wechat": {"_说明": "企业微信机器人配置 - 用于发送通知", "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_webhook_key", "_webhook_url说明": "企业微信机器人的Webhook地址"}, "ocr_validation": {"_说明": "OCR校验业务配置", "responsible_persons": {"completeness": "负责完备性检查的人员姓名", "accuracy": "负责准确性检查的人员姓名", "ocr_validation": "负责OCR校验的人员姓名"}, "processing_deadline_hours": 48, "_processing_deadline_hours说明": "任务处理截止时间（小时）", "check_interval_hours": 1, "_check_interval_hours说明": "检查间隔时间（小时）", "check_duration_hours": 24, "_check_duration_hours说明": "持续检查时长（小时）", "status_check_interval_minutes": 5, "_status_check_interval_minutes说明": "状态检查间隔（分钟）", "monitor_duration_hours": 72, "_monitor_duration_hours说明": "监控持续时间（小时）", "cc_list": ["抄送人1", "抄送人2"], "_cc_list说明": "任务抄送人员列表", "followup_rules": [{"label": "准确性-影像件模糊", "new_task_title": "调整ocr通过阈值", "owner": "负责人姓名"}, {"label": "完备性-缺件", "new_task_title": "补传缺失影像件", "owner": "负责人姓名"}], "_followup_rules说明": "后续处理规则配置", "followup_monitor_interval": 10, "_followup_monitor_interval说明": "后续监控间隔（分钟）"}, "web_server": {"_说明": "Web服务器配置", "host": "0.0.0.0", "_host说明": "Web服务器监听地址，0.0.0.0表示所有网络接口", "port": 5001, "_port说明": "Web服务器端口号", "debug": true, "_debug说明": "是否开启调试模式，生产环境建议设为false"}}