#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR服务 - Core层
处理OCR校验相关的业务逻辑
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

from utils import CSVProcessor, ConfigLoader, data_cache
from .tapd_service import TAPDService


class OCRService:
    """OCR业务服务"""
    
    def __init__(self, config_loader: Optional[ConfigLoader] = None):
        """
        初始化OCR服务
        
        Args:
            config_loader: 配置加载器实例
        """
        self.logger = logging.getLogger(__name__)
        self.config_loader = config_loader or ConfigLoader()
        self.csv_processor = CSVProcessor()
        self.tapd_service = TAPDService(self.config_loader)
        
        # 获取配置
        self.ocr_config = self.config_loader.get_ocr_config()
        self.responsible_persons = self.config_loader.get_responsible_persons()
        
        self.logger.info("OCR服务初始化完成")
    
    async def process_csv_validation_data(self, csv_file_path: str) -> Dict[str, Any]:
        """
        处理CSV校验数据
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            处理结果
        """
        try:
            # 分析CSV数据
            analysis_result = self.csv_processor.analyze_csv_data(csv_file_path)
            
            self.logger.info(f"CSV校验数据处理完成: {csv_file_path}")
            return {"success": True, "analysis_result": analysis_result}
            
        except Exception as e:
            self.logger.error(f"处理CSV校验数据失败: {e}")
            return {"success": False, "message": f"处理失败: {str(e)}"}
    
    def get_ocr_problem_types(self) -> List[Dict[str, Any]]:
        """
        获取OCR问题类型列表
        
        Returns:
            问题类型列表
        """
        return [
            {
                'type': 'accuracy_image_blur',
                'value': 'accuracy_image_blur',
                'label': '影像件模糊',
                'color': 'danger',
                'category': '准确性',
                'description': '影像件存在模糊问题'
            },
            {
                'type': 'accuracy_content_error',
                'value': 'accuracy_content_error',
                'label': '文字识别错误',
                'color': 'warning',
                'category': '准确性',
                'description': 'OCR识别内容与实际不符'
            },
            {
                'type': 'completeness_missing',
                'value': 'completeness_missing',
                'label': '缺件',
                'color': 'info',
                'category': '完备性',
                'description': '缺少必要的影像件'
            },
            {
                'type': 'completeness_extra',
                'value': 'completeness_extra',
                'label': '多件',
                'color': 'info',
                'category': '完备性',
                'description': '存在多余的影像件'
            },
            {
                'type': 'accuracy_page_error',
                'value': 'accuracy_page_error',
                'label': '页面识别错误',
                'color': 'warning',
                'category': '准确性',
                'description': '页面识别错误'
            },
            {
                'type': 'accuracy_format_error',
                'value': 'accuracy_format_error',
                'label': '格式错误',
                'color': 'warning',
                'category': '准确性',
                'description': '格式错误'
            },
            {
                'type': 'system_issue',
                'value': 'system_issue',
                'label': '系统问题',
                'color': 'secondary',
                'category': '系统',
                'description': '系统或算法问题'
            },
            {
                'type': 'no_issue',
                'value': 'no_issue',
                'label': '无问题',
                'color': 'success',
                'category': '验证',
                'description': '检查无误'
            }
        ]

    def validate_problem_tags(self, tags: List[str]) -> Dict[str, Any]:
        """
        验证问题标签
        
        Args:
            tags: 标签列表
            
        Returns:
            验证结果
        """
        # 获取有效的问题类型，支持type和value两种格式
        problem_types = self.get_ocr_problem_types()
        valid_types = set()
        
        for pt in problem_types:
            # 同时支持type和value字段
            if 'type' in pt:
                valid_types.add(pt['type'])
            if 'value' in pt:
                valid_types.add(pt['value'])
        
        validation_result = {
            "is_valid": True,
            "valid_tags": [],
            "invalid_tags": [],
            "warnings": []
        }
        
        for tag in tags:
            if tag in valid_types:
                validation_result["valid_tags"].append(tag)
            else:
                validation_result["invalid_tags"].append(tag)
                validation_result["is_valid"] = False
        
        if validation_result["invalid_tags"]:
            validation_result["warnings"].append(
                f"无效的问题类型: {', '.join(validation_result['invalid_tags'])}"
            )
        
        return validation_result
    
    async def save_task_problem_tags(self, task_id: str, problem_tags: List[str]) -> Dict[str, Any]:
        """
        保存任务的问题标签
        
        Args:
            task_id: 任务ID
            problem_tags: 问题标签列表
            
        Returns:
            保存结果
        """
        try:
            # 将标签转换为TAPD标签格式（逗号分隔）
            label_str = ','.join(problem_tags) if problem_tags else ''
            
            self.logger.info(f"保存任务标签: {task_id}, 标签: {label_str}")
            
            # 根据TAPD API文档，任务的标签字段应该是 'label'
            # 参考: https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/tasks/tasks.html
            result = await self.tapd_service.tapd_client.update_task(
                workspace_id=self.tapd_service.workspace_id,
                task_id=task_id,
                label=label_str
            )
            
            # 处理返回数据结构 - 照着get_task_details的修复方式
            if isinstance(result, dict):
                status = result.get("status")
                info = result.get("info")
            else:
                # 如果返回的是其他格式，尝试转换为dict
                self.logger.warning(f"API返回数据格式异常: {type(result)}, 内容: {result}")
                status = getattr(result, 'status', None) if hasattr(result, 'status') else None
                info = getattr(result, 'info', None) if hasattr(result, 'info') else None
                
                if status is None:
                    status = 0
                    info = f"数据格式异常: {result}"
            
            if status == 1:
                self.logger.info(f"任务标签保存成功: {task_id}, 标签: {label_str}")
                return {"success": True, "message": "标签保存成功"}
            else:
                error_info = info if info else '未知错误'
                self.logger.error(f"任务标签保存失败: {task_id}, 错误: {error_info}")
                
                # 如果 label 字段失败，尝试使用 labels 字段作为备用
                if 'label' in str(error_info).lower() or 'field' in str(error_info).lower():
                    self.logger.info(f"尝试使用 labels 字段保存标签: {task_id}")
                    result_backup = await self.tapd_service.tapd_client.update_task(
                        workspace_id=self.tapd_service.workspace_id,
                        task_id=task_id,
                        labels=label_str
                    )
                    
                    # 同样处理备用请求的返回数据结构
                    if isinstance(result_backup, dict):
                        backup_status = result_backup.get("status")
                        backup_info = result_backup.get("info")
                    else:
                        self.logger.warning(f"备用API返回数据格式异常: {type(result_backup)}, 内容: {result_backup}")
                        backup_status = getattr(result_backup, 'status', None) if hasattr(result_backup, 'status') else None
                        backup_info = getattr(result_backup, 'info', None) if hasattr(result_backup, 'info') else None
                        
                        if backup_status is None:
                            backup_status = 0
                            backup_info = f"数据格式异常: {result_backup}"
                    
                    if backup_status == 1:
                        self.logger.info(f"使用 labels 字段保存成功: {task_id}, 标签: {label_str}")
                        return {"success": True, "message": "标签保存成功（使用labels字段）"}
                    else:
                        self.logger.error(f"备用方式也失败: {backup_info}")
                
                return {"success": False, "message": f"保存失败: {error_info}"}
                
        except Exception as e:
            self.logger.error(f"保存任务标签异常: {e}")
            return {"success": False, "message": f"保存异常: {str(e)}"}
    
    async def get_task_problem_tags(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务的问题标签
        
        Args:
            task_id: 任务ID
            
        Returns:
            标签信息
        """
        try:
            result = await self.tapd_service.get_task_details(task_id)
            
            if result.get("success"):
                task_data = result.get("task", {})
                
                # 根据TAPD API文档，优先从 'label' 字段获取标签，然后尝试 'labels'
                label_str = ""
                source_field = ""
                
                if 'label' in task_data and task_data.get('label'):
                    label_str = task_data.get('label', "")
                    source_field = 'label'
                elif 'labels' in task_data and task_data.get('labels'):
                    label_str = task_data.get('labels', "")
                    source_field = 'labels'
                
                self.logger.info(f"从字段 {source_field} 获取到标签: {label_str}")
                
                # 解析标签字符串 - TAPD标签通常以逗号分隔
                tags = []
                if label_str:
                    # 处理可能的分隔符：逗号、分号、空格等
                    import re
                    # 先按逗号分割，然后清理每个标签
                    raw_tags = re.split(r'[,;，；\s]+', str(label_str))
                    tags = [tag.strip() for tag in raw_tags if tag.strip()]
                
                return {
                    "success": True,
                    "tags": tags,
                    "label_string": label_str,  # 返回原始标签字符串
                    "source_field": source_field,  # 返回标签来源字段
                    "updated_at": task_data.get("modified", "")
                }
            else:
                return {"success": False, "message": result.get("message", "获取任务信息失败")}
                
        except Exception as e:
            self.logger.error(f"获取任务标签异常: {e}")
            return {"success": False, "message": f"获取失败: {str(e)}"}
    
    async def get_ocr_workflow_status(self) -> Dict[str, Any]:
        """
        获取OCR工作流状态
        
        Returns:
            工作流状态信息
        """
        try:
            # 获取所有相关任务
            workflow_tasks = await self.tapd_service.get_workflow_tasks()
            
            # 定义工作流阶段
            workflow_stages = [
                {
                    'name': '告警创建',
                    'keywords': ['告警', '创建', 'TAPD', '需求'],
                    'responsible': '系统自动',
                    'category': 'alert_creation'
                },
                {
                    'name': '判断准确性问题类型',
                    'keywords': ['准确性', '判断', '类型', '分类'],
                    'responsible': '李玲',
                    'category': 'accuracy_type_identification'
                },
                {
                    'name': '完备性问题处理',
                    'keywords': ['完备性', '缺失', '影像件', '补传'],
                    'responsible': '司马维维',
                    'category': 'completeness_handling'
                },
                {
                    'name': '影像件模糊处理',
                    'keywords': ['影像', '模糊', '阈值', '调整'],
                    'responsible': '李玲',
                    'category': 'image_quality_handling'
                },
                {
                    'name': '内容错误处理',
                    'keywords': ['内容', '错误', '沟通', '机构'],
                    'responsible': '司马维维',
                    'category': 'content_error_handling'
                },
                {
                    'name': '系统问题修复',
                    'keywords': ['系统', '修复', 'bug', '算法'],
                    'responsible': '李玲',
                    'category': 'system_issue_handling'
                },
                {
                    'name': '重新OCR识别',
                    'keywords': ['重新', '识别', 'OCR', '发起'],
                    'responsible': '陈俊',
                    'category': 'ocr_reprocessing'
                },
                {
                    'name': '闭环验证',
                    'keywords': ['验证', '闭环', '检查', '完成'],
                    'responsible': '系统自动',
                    'category': 'verification'
                }
            ]
            
            # 统计各阶段状态
            stage_stats = []
            for stage in workflow_stages:
                stage_tasks = self._match_tasks_to_stage(workflow_tasks, stage)
                
                total_count = len(stage_tasks)
                completed_count = len([t for t in stage_tasks if t['status'] == '已完成'])
                completion_rate = (completed_count / total_count * 100) if total_count > 0 else 0
                
                # 确定阶段状态
                if total_count == 0:
                    stage_status = 'not_started'
                elif completed_count == total_count:
                    stage_status = 'completed'
                else:
                    stage_status = 'pending'
                
                stage_stats.append({
                    'name': stage['name'],
                    'status': stage_status,
                    'tasks_count': total_count,
                    'completed_count': completed_count,
                    'responsible': stage['responsible'],
                    'completion_rate': round(completion_rate, 1)
                })
            
            return {
                "success": True,
                "stages": stage_stats,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取OCR工作流状态失败: {e}")
            return {"success": False, "message": f"获取失败: {str(e)}"}
    
    def _match_tasks_to_stage(self, workflow_tasks: Dict[str, List], stage: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        将任务匹配到工作流阶段
        
        Args:
            workflow_tasks: 工作流任务字典
            stage: 阶段信息
            
        Returns:
            匹配的任务列表
        """
        matched_tasks = []
        keywords = stage.get('keywords', [])
        
        for person, tasks in workflow_tasks.items():
            for task in tasks:
                task_name = task.get('name', '').lower()
                
                # 检查任务名称是否包含关键词
                if any(keyword.lower() in task_name for keyword in keywords):
                    matched_tasks.append(task)
        
        return matched_tasks 