#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR监控系统统一主入口文件
包含所有功能：Web应用、文件监控、任务状态监控、即时后续任务创建
"""

import os
import sys
import logging
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app
from utils import ConfigLoader
from core import FileMonitorService, TaskStatusMonitor


def setup_directories():
    """创建必要的目录结构"""
    directories = [
        'data/cache',
        'data/logs', 
        'data/exports',
        'web/templates',
        'web/static/css',
        'web/static/js',
        'config',
        'docs/api_docs'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 确保目录存在: {directory}")


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('data/logs/app.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def print_startup_info():
    """打印启动信息"""
    print("\n🚀 OCR任务监控系统 v2.0 (完整版)", flush=True)
    print("=" * 60, flush=True)
    print("📊 采用全新的三层模块化架构：", flush=True)
    print("   🌐 App层：Flask Web应用和API路由", flush=True)
    print("   ⚙️  Core层：业务逻辑服务（TAPD、OCR、监控、文件监控）", flush=True)
    print("   🔧 Utils层：基础工具（客户端、缓存、配置）", flush=True)
    print("=" * 60, flush=True)
    print("✨ 完整版特性：", flush=True)
    print("   - 模块化架构，更好的代码组织", flush=True)
    print("   - 统一的配置管理", flush=True)
    print("   - 优化的缓存机制", flush=True)
    print("   - 文件监控功能，自动处理CSV文件", flush=True)
    print("   - 任务状态监控，自动创建后续任务", flush=True)
    print("   - 即时标签变化处理，实时响应用户操作", flush=True)
    print("   - 自动创建TAPD需求和任务", flush=True)
    print("   - 清晰的依赖关系", flush=True)
    print("=" * 60, flush=True)


def main():
    """主函数"""
    try:
        # 打印启动信息
        print_startup_info()
        
        # 创建必要目录
        print("📁 初始化目录结构...")
        setup_directories()
        
        # 设置日志
        print("📝 配置日志系统...")
        setup_logging()
        
        # 加载配置
        print("⚙️  加载系统配置...")
        config_loader = ConfigLoader()
        
        # 获取Web服务配置
        host, port = config_loader.get_web_host_port()
        debug_mode = config_loader.is_debug_mode()
        
        print(f"🌐 Web服务配置：{host}:{port}")
        print(f"🔍 调试模式：{'开启' if debug_mode else '关闭'}")
        
        # 创建Flask应用
        print("🏗️  初始化Flask应用...", flush=True)
        app = create_app(config_loader)

        # 初始化文件监控服务
        print("📂 初始化文件监控服务...", flush=True)
        file_monitor = None
        try:
            file_monitor = FileMonitorService(config_loader)
            print("✅ 文件监控服务初始化成功", flush=True)
        except ImportError:
            print("⚠️  文件监控服务初始化失败，可能需要安装watchdog库:", flush=True)
            print("    pip install watchdog", flush=True)
        except Exception as e:
            print(f"⚠️  文件监控服务初始化失败: {e}", flush=True)

        # 初始化任务状态监控服务
        print("⏰ 初始化任务状态监控服务...", flush=True)
        task_status_monitor = TaskStatusMonitor(config_loader)
        print("✅ 任务状态监控服务初始化成功", flush=True)

        # 将监控服务注册到应用（这是关键！确保API可以访问）
        app.file_monitor = file_monitor
        app.task_status_monitor = task_status_monitor

        # 启动文件监控
        if file_monitor:
            print("🔍 启动文件监控...")
            monitor_started = file_monitor.start_monitoring()

            if monitor_started:
                print("✅ 文件监控启动成功")

                # 扫描现有文件
                print("📋 扫描现有文件...")
                try:
                    asyncio.run(file_monitor.scan_existing_files())
                    print("✅ 现有文件扫描完成")
                except Exception as e:
                    print(f"⚠️  扫描现有文件时出错: {e}")
            else:
                print("⚠️  文件监控启动失败")

        # 启动任务状态监控（定时监控，作为即时响应的备份）
        print("⏰ 启动任务状态监控...")
        task_monitor_started = task_status_monitor.start_monitoring()

        if task_monitor_started:
            print("✅ 任务状态监控启动成功")
        else:
            print("⚠️  任务状态监控启动失败")

        print("=" * 60)
        print(f"📋 访问地址：")
        print(f"   人员列表：http://localhost:{port}/")
        print(f"   (点击人员姓名进入专属看板)")
        print("=" * 60)
        print("🔄 正在连接TAPD获取真实数据...")
        print("✅ 系统启动完成！")
        print("=" * 60)
        print("🎯 功能说明：")
        print("   - 📁 文件监控：自动处理ocr_doublecheck目录下的CSV文件")
        print("   - ⏰ 任务监控：定时检查任务状态变化")
        print("   - ⚡ 即时响应：用户添加标签时立即创建后续任务")
        print("   - 🔄 批次检测：自动检查批次完成状态")
        print("=" * 60)

        # 启动Web服务
        try:
            # 在调试模式下禁用自动重载以避免重复启动
            use_reloader = debug_mode and os.environ.get('WERKZEUG_RUN_MAIN') != 'true'
            app.run(host=host, port=port, debug=debug_mode, use_reloader=use_reloader)
        finally:
            # 确保监控服务停止
            if file_monitor and file_monitor.is_monitoring:
                print("\n🛑 正在停止文件监控...")
                file_monitor.stop_monitoring()

            if task_status_monitor and task_status_monitor.is_monitoring:
                print("\n🛑 正在停止任务状态监控...")
                task_status_monitor.stop_monitoring()
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，系统正在关闭...")
    except Exception as e:
        print(f"\n❌ 系统启动失败：{e}")
        logging.error(f"系统启动失败: {e}", exc_info=True)
        sys.exit(1)


if __name__ == '__main__':
    main() 