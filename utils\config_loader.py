#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置加载器 - Utils层
统一的配置文件加载和管理，支持多种配置源
"""

import json
import logging
import os
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化配置加载器
        
        Args:
            config_file: 配置文件路径，默认为config/config.json
        """
        self.logger = logging.getLogger(__name__)
        
        # 确定配置文件路径
        if config_file:
            self.config_file = config_file
        else:
            # 优先从config目录加载，兼容旧位置
            config_dir_path = Path("config/config.json")
            root_dir_path = Path("config.json")
            
            if config_dir_path.exists():
                self.config_file = str(config_dir_path)
            elif root_dir_path.exists():
                self.config_file = str(root_dir_path)
            else:
                self.config_file = "config/config.json"
        
        self._config = None
        self._load_config()
        
        self.logger.info(f"配置加载器初始化完成，配置文件: {self.config_file}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self._config = json.load(f)
                self.logger.info(f"配置文件加载成功: {self.config_file}")
                return self._config
        except FileNotFoundError:
            self.logger.warning(f"配置文件不存在: {self.config_file}")
            self._config = {}
            return self._config
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件格式错误: {e}")
            self._config = {}
            return self._config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self._config = {}
            return self._config
    
    def reload_config(self) -> Dict[str, Any]:
        """重新加载配置"""
        self.logger.info("重新加载配置文件")
        return self._load_config()
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        if self._config is None:
            self._load_config()
        return self._config or {}
    
    def get_tapd_config(self) -> Dict[str, Any]:
        """获取TAPD配置"""
        config = self.get_config()
        return config.get("tapd", {})
    
    def get_wechat_config(self) -> Dict[str, Any]:
        """获取企业微信配置"""
        config = self.get_config()
        return config.get("wechat", {})
    
    def get_ocr_config(self) -> Dict[str, Any]:
        """获取OCR配置"""
        config = self.get_config()
        return config.get("ocr_validation", {})
    
    def get_web_config(self) -> Dict[str, Any]:
        """获取Web服务配置"""
        config = self.get_config()
        return config.get("web_server", {})
    
    def get_responsible_persons(self) -> Dict[str, str]:
        """获取负责人配置"""
        ocr_config = self.get_ocr_config()
        return ocr_config.get("responsible_persons", {})
    
    def get_role_mapping(self) -> Dict[str, str]:
        """获取角色映射配置"""
        ocr_config = self.get_ocr_config()
        return ocr_config.get("role_mapping", {})
    
    def get_actual_person_by_role(self, role: str) -> str:
        """根据角色获取实际负责人"""
        responsible_persons = self.get_responsible_persons()
        return responsible_persons.get(role, "")
    
    def get_role_name_by_role(self, role: str) -> str:
        """根据角色获取角色名称"""
        role_mapping = self.get_role_mapping()
        return role_mapping.get(role, role)
    
    def get_local_ip(self) -> str:
        """获取本地IP配置"""
        web_config = self.get_web_config()
        return web_config.get("local_ip", "localhost")
    
    def get_workspace_id(self) -> Optional[int]:
        """获取TAPD工作空间ID"""
        tapd_config = self.get_tapd_config()
        workspace_id = tapd_config.get("workspace_id")
        if workspace_id:
            return int(workspace_id)
        return None
    
    def get_wechat_webhook_url(self) -> Optional[str]:
        """获取企业微信Webhook URL"""
        wechat_config = self.get_wechat_config()
        return wechat_config.get("webhook_url")
    
    def get_web_host_port(self) -> tuple:
        """获取Web服务主机和端口"""
        web_config = self.get_web_config()
        host = web_config.get("host", "0.0.0.0")
        port = web_config.get("port", 5001)
        return host, port
    
    def is_debug_mode(self) -> bool:
        """是否为调试模式"""
        web_config = self.get_web_config()
        return web_config.get("debug", False)
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置完整性"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        config = self.get_config()
        
        # 验证TAPD配置
        tapd_config = config.get("tapd", {})
        if not tapd_config.get("api_user"):
            validation_result["errors"].append("TAPD api_user配置缺失")
            validation_result["is_valid"] = False
        
        if not tapd_config.get("api_password"):
            validation_result["errors"].append("TAPD api_password配置缺失")
            validation_result["is_valid"] = False
        
        if not tapd_config.get("workspace_id"):
            validation_result["errors"].append("TAPD workspace_id配置缺失")
            validation_result["is_valid"] = False
        
        # 验证负责人配置
        responsible_persons = self.get_responsible_persons()
        if not responsible_persons:
            validation_result["warnings"].append("负责人配置为空")
        
        # 验证企业微信配置（可选）
        wechat_config = config.get("wechat", {})
        if not wechat_config.get("webhook_url"):
            validation_result["warnings"].append("企业微信Webhook URL未配置")
        
        return validation_result
    
    def save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        try:
            # 确保目录存在
            config_dir = Path(self.config_file).parent
            config_dir.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self._config = config
            self.logger.info(f"配置文件保存成功: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            raise 