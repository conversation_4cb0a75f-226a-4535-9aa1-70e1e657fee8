2025-07-16 09:52:19,231 - utils.cache - INFO - 从缓存文件加载数据: 5 个条目
2025-07-16 09:52:19,232 - utils.cache - INFO - 数据缓存管理器初始化完成，缓存文件: data\cache\data_cache.json
2025-07-16 09:52:20,165 - __main__ - INFO - 创建Flask应用...
2025-07-16 09:52:20,167 - utils.config_loader - INFO - 配置文件加载成功: config\config.json
2025-07-16 09:52:20,167 - utils.config_loader - INFO - 配置加载器初始化完成，配置文件: config\config.json
2025-07-16 09:52:20,167 - utils.tapd_client - INFO - TAPD认证配置完成: 6CCSRI0S
2025-07-16 09:52:20,168 - utils.tapd_client - INFO - TAPD客户端初始化完成
2025-07-16 09:52:20,168 - core.tapd_service - INFO - TAPD业务服务初始化完成
2025-07-16 09:52:20,168 - utils.tapd_client - INFO - TAPD认证配置完成: 6CCSRI0S
2025-07-16 09:52:20,168 - utils.tapd_client - INFO - TAPD客户端初始化完成
2025-07-16 09:52:20,169 - core.tapd_service - INFO - TAPD业务服务初始化完成
2025-07-16 09:52:20,169 - core.ocr_service - INFO - OCR服务初始化完成
2025-07-16 09:52:20,169 - core.monitor_service - INFO - 后台缓存刷新任务已启动（每5分钟完全刷新）
2025-07-16 09:52:20,170 - core.monitor_service - INFO - OCR监控服务初始化完成
2025-07-16 09:52:20,179 - root - INFO - Flask应用初始化完成
2025-07-16 09:52:20,179 - __main__ - INFO - 启动应用，地址: http://localhost:5000
2025-07-16 09:52:20,179 - __main__ - INFO - 提示: 文件监控功能需要安装 watchdog 库: pip install watchdog
2025-07-16 09:52:20,190 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 09:52:20,190 - utils.cache - INFO - 所有缓存已清除
2025-07-16 09:52:20,193 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 09:52:20,220 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-16 09:52:20,220 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-16 09:52:20,772 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 09:52:20,774 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 09:52:20,775 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 09:52:20,903 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 09:52:20,905 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 09:52:20,905 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 09:52:21,041 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:52:21,042 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:52:21,042 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 09:52:21,193 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 09:52:21,194 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 09:52:21,355 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 09:52:21,355 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 09:52:21,473 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:52:21,474 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:52:21,595 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 09:52:21,601 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 09:52:21,724 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 09:52:21,725 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 09:52:21,852 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:52:21,853 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:52:21,855 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 09:52:44,192 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:44] "GET / HTTP/1.1" 200 -
2025-07-16 09:52:44,549 - app.web_app - WARNING - 无效的人员代码: favicon.ico
2025-07-16 09:52:44,550 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:44] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-16 09:52:47,516 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:47] "GET /c9994a50 HTTP/1.1" 200 -
2025-07-16 09:52:47,754 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:47] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-16 09:52:47,919 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:47] "GET /static/js/main.js HTTP/1.1" 200 -
2025-07-16 09:52:48,335 - utils.tapd_client - INFO - 获取工作空间 38183800 的任务字段配置
2025-07-16 09:52:48,428 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/task_fields_settings?workspace_id=38183800 "HTTP/1.1 200 OK"
2025-07-16 09:52:48,430 - utils.tapd_client - INFO - 任务字段配置获取成功
2025-07-16 09:52:48,432 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:48] "GET /api/tapd/task_fields HTTP/1.1" 200 -
2025-07-16 09:52:48,579 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:48] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 09:52:48,774 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:48] "GET /api/config HTTP/1.1" 200 -
2025-07-16 09:52:48,818 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 09:52:48,819 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:48] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 09:52:49,127 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 09:52:49,128 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:49] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 09:52:49,295 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:52:49,297 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:52:49,297 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 09:52:49,301 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:49] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 09:52:49,647 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:49] "GET /api/ocr_problem_types HTTP/1.1" 200 -
2025-07-16 09:52:54,973 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:54] "GET /ebc38745 HTTP/1.1" 200 -
2025-07-16 09:52:55,261 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-16 09:52:55,340 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:55] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-07-16 09:52:55,682 - utils.tapd_client - INFO - 获取工作空间 38183800 的任务字段配置
2025-07-16 09:52:55,786 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/task_fields_settings?workspace_id=38183800 "HTTP/1.1 200 OK"
2025-07-16 09:52:55,787 - utils.tapd_client - INFO - 任务字段配置获取成功
2025-07-16 09:52:55,788 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:55] "GET /api/tapd/task_fields HTTP/1.1" 200 -
2025-07-16 09:52:55,934 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:55] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 09:52:56,129 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:56] "GET /api/config HTTP/1.1" 200 -
2025-07-16 09:52:56,166 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 09:52:56,166 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:56] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 09:52:56,483 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 09:52:56,485 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:56] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 09:52:56,621 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:52:56,623 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:52:56,623 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 09:52:56,627 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:56] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 09:52:56,941 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:52:56] "GET /api/ocr_problem_types HTTP/1.1" 200 -
2025-07-16 09:53:13,245 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&id=1138183800001000846 "HTTP/1.1 200 OK"
2025-07-16 09:53:13,246 - core.ocr_service - INFO - 从字段  获取到标签: 
2025-07-16 09:53:13,246 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:53:13] "GET /api/task/1138183800001000846/tags HTTP/1.1" 200 -
2025-07-16 09:53:17,586 - core.ocr_service - INFO - 保存任务标签: 1138183800001000846, 标签: accuracy_content_error
2025-07-16 09:53:17,772 - httpx - INFO - HTTP Request: POST https://api.tapd.cn/tasks "HTTP/1.1 200 OK"
2025-07-16 09:53:17,775 - utils.tapd_client - INFO - 任务更新成功: 1138183800001000846
2025-07-16 09:53:17,778 - core.ocr_service - INFO - 任务标签保存成功: 1138183800001000846, 标签: accuracy_content_error
2025-07-16 09:53:17,789 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:53:17] "POST /api/task/save_tags HTTP/1.1" 200 -
2025-07-16 09:53:55,094 - utils.cache - INFO - 从缓存文件加载数据: 0 个条目
2025-07-16 09:53:55,094 - utils.cache - INFO - 数据缓存管理器初始化完成，缓存文件: data\cache\data_cache.json
2025-07-16 09:53:56,005 - __main__ - INFO - 创建Flask应用...
2025-07-16 09:53:56,007 - utils.config_loader - INFO - 配置文件加载成功: config\config.json
2025-07-16 09:53:56,007 - utils.config_loader - INFO - 配置加载器初始化完成，配置文件: config\config.json
2025-07-16 09:53:56,007 - utils.tapd_client - INFO - TAPD认证配置完成: 6CCSRI0S
2025-07-16 09:53:56,008 - utils.tapd_client - INFO - TAPD客户端初始化完成
2025-07-16 09:53:56,008 - core.tapd_service - INFO - TAPD业务服务初始化完成
2025-07-16 09:53:56,008 - utils.tapd_client - INFO - TAPD认证配置完成: 6CCSRI0S
2025-07-16 09:53:56,008 - utils.tapd_client - INFO - TAPD客户端初始化完成
2025-07-16 09:53:56,008 - core.tapd_service - INFO - TAPD业务服务初始化完成
2025-07-16 09:53:56,008 - core.ocr_service - INFO - OCR服务初始化完成
2025-07-16 09:53:56,009 - core.monitor_service - INFO - 后台缓存刷新任务已启动（每5分钟完全刷新）
2025-07-16 09:53:56,009 - core.monitor_service - INFO - OCR监控服务初始化完成
2025-07-16 09:53:56,017 - root - INFO - Flask应用初始化完成
2025-07-16 09:53:56,017 - __main__ - INFO - 启动应用，地址: http://localhost:5000
2025-07-16 09:53:56,017 - __main__ - INFO - 提示: 文件监控功能需要安装 watchdog 库: pip install watchdog
2025-07-16 09:53:56,031 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 09:53:56,031 - utils.cache - INFO - 所有缓存已清除
2025-07-16 09:53:56,033 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 09:53:56,054 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-16 09:53:56,055 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-16 09:53:56,588 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 09:53:56,589 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 09:53:56,590 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 09:53:56,715 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 09:53:56,716 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 09:53:56,716 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 09:53:56,872 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:53:56,872 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:53:56,873 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 09:53:57,003 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 09:53:57,006 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 09:53:57,167 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 09:53:57,169 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 09:53:57,327 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:53:57,328 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:53:57,450 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 09:53:57,456 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 09:53:57,625 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 09:53:57,632 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 09:53:57,752 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:53:57,753 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:53:57,756 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 09:58:57,762 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 09:58:57,762 - utils.cache - INFO - 所有缓存已清除
2025-07-16 09:58:57,764 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 09:58:57,959 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 09:58:57,959 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 09:58:57,960 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 09:58:58,108 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 09:58:58,109 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 09:58:58,109 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 09:58:58,256 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:58:58,257 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:58:58,257 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 09:58:58,382 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 09:58:58,385 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 09:58:58,548 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 09:58:58,549 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 09:58:58,659 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:58:58,659 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:58:58,817 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 09:58:58,820 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 09:58:58,963 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 09:58:58,967 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 09:58:59,113 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 09:58:59,114 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 09:58:59,116 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:00:43,767 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:43] "GET /ebc38745 HTTP/1.1" 200 -
2025-07-16 10:00:43,877 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-16 10:00:44,014 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:44] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-07-16 10:00:44,492 - utils.tapd_client - INFO - 获取工作空间 38183800 的任务字段配置
2025-07-16 10:00:44,586 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/task_fields_settings?workspace_id=38183800 "HTTP/1.1 200 OK"
2025-07-16 10:00:44,587 - utils.tapd_client - INFO - 任务字段配置获取成功
2025-07-16 10:00:44,588 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:44] "GET /api/tapd/task_fields HTTP/1.1" 200 -
2025-07-16 10:00:44,745 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:44] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 10:00:44,944 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:44] "GET /api/config HTTP/1.1" 200 -
2025-07-16 10:00:44,967 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 10:00:44,968 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:44] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 10:00:45,284 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 10:00:45,285 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:45] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 10:00:45,415 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:00:45,416 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:00:45,416 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:00:45,420 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:45] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 10:00:45,736 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:45] "GET /api/ocr_problem_types HTTP/1.1" 200 -
2025-07-16 10:00:51,897 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&id=1138183800001000846 "HTTP/1.1 200 OK"
2025-07-16 10:00:51,898 - core.ocr_service - INFO - 从字段  获取到标签: 
2025-07-16 10:00:51,899 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:51] "GET /api/task/1138183800001000846/tags HTTP/1.1" 200 -
2025-07-16 10:00:55,570 - core.ocr_service - INFO - 保存任务标签: 1138183800001000846, 标签: 
2025-07-16 10:00:55,870 - httpx - INFO - HTTP Request: POST https://api.tapd.cn/tasks "HTTP/1.1 200 OK"
2025-07-16 10:00:55,870 - utils.tapd_client - INFO - 任务更新成功: 1138183800001000846
2025-07-16 10:00:55,871 - core.ocr_service - INFO - 任务标签保存成功: 1138183800001000846, 标签: 
2025-07-16 10:00:55,880 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:00:55] "POST /api/task/save_tags HTTP/1.1" 200 -
2025-07-16 10:01:01,004 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:01] "GET /ebc38745 HTTP/1.1" 200 -
2025-07-16 10:01:01,305 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-16 10:01:01,347 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:01] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-07-16 10:01:01,710 - utils.tapd_client - INFO - 获取工作空间 38183800 的任务字段配置
2025-07-16 10:01:01,798 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/task_fields_settings?workspace_id=38183800 "HTTP/1.1 200 OK"
2025-07-16 10:01:01,799 - utils.tapd_client - INFO - 任务字段配置获取成功
2025-07-16 10:01:01,800 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:01] "GET /api/tapd/task_fields HTTP/1.1" 200 -
2025-07-16 10:01:02,091 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:01:02,092 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:01:02,112 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:02] "GET /api/config HTTP/1.1" 200 -
2025-07-16 10:01:02,230 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:01:02,230 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:01:02,389 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:01:02,390 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:01:02,392 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:02] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 10:01:02,577 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:01:02,580 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:01:02,580 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:01:02,586 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:02] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 10:01:02,851 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:01:02,852 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:01:02,853 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:01:02,858 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:02] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 10:01:02,882 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:01:02,883 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:01:02,884 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:01:02,887 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:02] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 10:01:03,199 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:01:03] "GET /api/ocr_problem_types HTTP/1.1" 200 -
2025-07-16 10:03:59,131 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:03:59,131 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:03:59,133 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:03:59,309 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:03:59,310 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:03:59,310 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:03:59,471 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:03:59,472 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:03:59,472 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:03:59,592 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:03:59,593 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:03:59,593 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:03:59,753 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:03:59,754 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:03:59,910 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:03:59,913 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:04:00,053 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:04:00,054 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:04:00,187 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:04:00,189 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:04:00,304 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:04:00,306 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:04:00,461 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:04:00,464 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:04:00,468 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:06:03,747 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:06:03] "GET /api/config HTTP/1.1" 200 -
2025-07-16 10:06:03,762 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:06:03] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 10:06:04,009 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 10:06:04,010 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:06:04] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 10:06:04,095 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 10:06:04,097 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:06:04] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 10:06:04,258 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:06:04,259 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:06:04,259 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:06:04,262 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:06:04] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 10:09:00,483 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:09:00,483 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:09:00,486 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:09:00,678 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:09:00,679 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:09:00,680 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:09:00,807 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:09:00,808 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:09:00,809 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:09:00,967 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:09:00,970 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:09:00,972 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:09:01,102 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:09:01,103 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:09:01,261 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:09:01,264 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:09:01,419 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:09:01,420 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:09:01,568 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:09:01,569 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:09:01,725 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:09:01,729 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:09:01,902 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:09:01,903 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:09:01,906 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:11:03,749 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:11:03] "GET /api/config HTTP/1.1" 200 -
2025-07-16 10:11:03,762 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:11:03] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 10:11:04,008 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 10:11:04,008 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:11:04] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 10:11:04,126 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 10:11:04,126 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:11:04] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 10:11:04,279 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:11:04,280 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:11:04,280 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:11:04,282 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:11:04] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 10:14:01,933 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:14:01,934 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:14:01,936 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:14:02,114 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:14:02,117 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:14:02,119 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:14:02,260 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:14:02,262 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:14:02,264 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:14:02,422 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:14:02,423 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:14:02,423 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:14:02,554 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:14:02,555 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:14:02,723 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:14:02,724 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:14:02,883 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:14:02,884 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:14:03,057 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:14:03,058 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:14:03,216 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:14:03,218 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:14:03,383 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:14:03,384 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:14:03,386 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:16:03,750 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:16:03] "GET /api/config HTTP/1.1" 200 -
2025-07-16 10:16:03,763 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:16:03] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 10:16:04,007 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 10:16:04,008 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:16:04] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 10:16:04,098 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 10:16:04,099 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:16:04] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 10:16:04,275 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:16:04,278 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:16:04,278 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:16:04,288 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:16:04] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 10:19:03,404 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:19:03,405 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:19:03,408 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:19:03,610 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:19:03,611 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:19:03,611 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:19:03,739 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:19:03,740 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:19:03,740 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:19:03,896 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:19:03,897 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:19:03,897 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:19:04,023 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:19:04,024 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:19:04,154 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:19:04,155 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:19:04,312 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:19:04,312 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:19:04,450 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:19:04,451 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:19:04,607 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:19:04,608 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:19:04,754 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:19:04,755 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:19:04,757 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:21:03,746 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:21:03] "GET /api/config HTTP/1.1" 200 -
2025-07-16 10:21:03,763 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:21:03] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 10:21:04,015 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 10:21:04,016 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:21:04] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 10:21:04,106 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 10:21:04,107 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:21:04] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 10:21:04,244 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:21:04,247 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:21:04,248 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:21:04,252 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:21:04] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 10:24:04,772 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:24:04,772 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:24:04,775 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:24:04,907 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:24:04,908 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:24:04,908 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:24:05,068 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:24:05,069 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:24:05,069 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:24:05,220 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:24:05,220 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:24:05,221 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:24:05,348 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:24:05,349 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:24:05,482 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:24:05,486 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:24:05,607 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:24:05,608 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:24:05,726 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:24:05,727 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:24:05,892 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:24:05,893 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:24:06,015 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:24:06,016 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:24:06,018 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:26:03,756 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:26:03] "GET /api/config HTTP/1.1" 200 -
2025-07-16 10:26:03,768 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:26:03] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 10:26:04,018 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 10:26:04,019 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:26:04] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 10:26:04,102 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 10:26:04,103 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:26:04] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 10:26:04,270 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:26:04,271 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:26:04,271 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:26:04,274 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:26:04] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 10:29:06,031 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:29:06,032 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:29:06,034 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:29:06,279 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:29:06,280 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:29:06,280 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:29:06,443 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:29:06,444 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:29:06,444 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:29:06,598 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:29:06,598 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:29:06,599 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:29:06,731 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:29:06,732 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:29:06,872 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:29:06,873 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:29:07,037 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:29:07,037 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:29:07,160 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:29:07,161 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:29:07,314 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:29:07,315 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:29:07,466 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:29:07,467 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:29:07,469 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:31:03,756 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:31:03] "GET /api/config HTTP/1.1" 200 -
2025-07-16 10:31:03,769 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:31:03] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 10:31:04,016 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 10:31:04,017 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:31:04] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 10:31:04,128 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 10:31:04,129 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:31:04] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 10:31:04,258 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:31:04,259 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:31:04,259 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:31:04,262 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:31:04] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 10:34:07,482 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:34:07,483 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:34:07,485 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:34:07,646 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:34:07,648 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:34:07,649 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:34:07,810 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:34:07,810 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:34:07,811 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:34:07,974 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:34:07,977 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:34:07,978 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:34:08,134 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:34:08,135 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:34:08,294 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:34:08,295 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:34:08,444 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:34:08,445 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:34:08,566 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:34:08,567 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:34:08,738 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:34:08,739 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:34:08,883 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:34:08,884 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:34:08,887 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:36:03,765 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:36:03] "GET /api/config HTTP/1.1" 200 -
2025-07-16 10:36:03,777 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:36:03] "GET /api/process_overview HTTP/1.1" 200 -
2025-07-16 10:36:04,005 - core.monitor_service - INFO - 从缓存获取 郑凯鑫 的任务数据
2025-07-16 10:36:04,006 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:36:04] "GET /api/person_tasks/郑凯鑫 HTTP/1.1" 200 -
2025-07-16 10:36:04,113 - core.monitor_service - INFO - 从缓存获取 刘青昀 的任务数据
2025-07-16 10:36:04,114 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:36:04] "GET /api/person_tasks/刘青昀 HTTP/1.1" 200 -
2025-07-16 10:36:04,254 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:36:04,254 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:36:04,255 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:36:04,257 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:36:04] "GET /api/person_tasks/陈嫚婷 HTTP/1.1" 200 -
2025-07-16 10:39:08,900 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:39:08,900 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:39:08,902 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:39:09,147 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:39:09,150 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:39:09,151 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:39:09,305 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:39:09,306 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:39:09,306 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:39:09,421 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:39:09,422 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:39:09,422 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:39:09,580 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:39:09,581 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:39:09,698 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:39:09,700 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:39:09,823 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:39:09,823 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:39:09,952 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:39:09,953 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:39:10,076 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:39:10,077 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:39:10,215 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:39:10,215 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:39:10,218 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:44:10,232 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:44:10,233 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:44:10,234 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:44:10,416 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:44:10,417 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:44:10,417 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:44:10,584 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:44:10,585 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:44:10,585 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:44:10,742 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:44:10,745 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:44:10,747 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:44:10,914 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:44:10,915 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:44:11,043 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:44:11,047 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:44:11,212 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:44:11,213 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:44:11,373 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:44:11,374 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:44:11,536 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:44:11,537 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:44:11,668 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:44:11,668 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:44:11,671 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:49:11,684 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:49:11,685 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:49:11,688 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:49:11,917 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:49:11,918 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:49:11,918 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:49:12,079 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:49:12,080 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:49:12,080 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:49:12,205 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:49:12,207 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:49:12,208 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:49:12,393 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:49:12,394 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:49:12,544 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:49:12,545 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:49:12,660 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:49:12,660 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:49:12,790 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:49:12,791 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:49:12,918 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:49:12,919 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:49:13,037 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:49:13,038 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:49:13,040 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:54:13,054 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:54:13,055 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:54:13,057 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:54:13,200 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:54:13,201 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:54:13,201 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:54:13,336 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:54:13,337 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:54:13,337 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:54:13,463 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:54:13,463 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:54:13,464 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:54:13,629 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:54:13,631 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:54:13,788 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:54:13,792 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:54:13,923 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:54:13,924 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:54:14,055 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:54:14,058 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:54:14,186 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:54:14,186 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:54:14,338 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:54:14,339 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:54:14,341 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 10:59:14,356 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 10:59:14,357 - utils.cache - INFO - 所有缓存已清除
2025-07-16 10:59:14,359 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 10:59:14,584 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:59:14,584 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:59:14,585 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 10:59:14,732 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:59:14,733 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:59:14,733 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 10:59:14,852 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:59:14,853 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:59:14,853 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 10:59:14,977 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:59:14,978 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:59:15,100 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:59:15,101 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:59:15,225 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:59:15,226 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:59:15,353 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 10:59:15,354 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 10:59:15,506 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 10:59:15,508 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 10:59:15,625 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 10:59:15,626 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 10:59:15,629 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 11:04:15,643 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 11:04:15,644 - utils.cache - INFO - 所有缓存已清除
2025-07-16 11:04:15,649 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 11:04:15,811 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:04:15,812 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:04:15,813 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 11:04:16,006 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:04:16,007 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:04:16,007 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 11:04:16,125 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:04:16,126 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:04:16,126 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 11:04:16,264 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:04:16,266 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:04:16,417 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:04:16,418 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:04:16,537 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:04:16,537 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:04:16,661 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:04:16,662 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:04:16,779 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:04:16,779 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:04:16,924 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:04:16,925 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:04:16,928 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 11:09:16,942 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 11:09:16,943 - utils.cache - INFO - 所有缓存已清除
2025-07-16 11:09:16,945 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 11:09:17,094 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:09:17,095 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:09:17,095 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 11:09:17,251 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:09:17,253 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:09:17,254 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 11:09:17,382 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:09:17,383 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:09:17,383 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 11:09:17,518 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:09:17,521 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:09:17,695 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:09:17,696 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:09:17,841 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:09:17,841 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:09:17,999 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:09:18,000 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:09:18,129 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:09:18,130 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:09:18,251 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:09:18,254 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:09:18,260 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 11:14:18,276 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 11:14:18,276 - utils.cache - INFO - 所有缓存已清除
2025-07-16 11:14:18,280 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 11:14:23,296 - utils.tapd_client - ERROR - 获取任务异常: 
2025-07-16 11:14:23,306 - core.tapd_service - ERROR - 获取 刘青昀 的任务失败: {'status': 'error', 'message': ''}
2025-07-16 11:14:23,308 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 11:14:23,473 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:14:23,474 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:14:23,474 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 11:14:23,638 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:14:23,639 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:14:23,639 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 11:14:23,788 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:14:23,789 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:14:23,914 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:14:23,915 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:14:24,032 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:14:24,033 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:14:24,173 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:14:24,174 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:14:24,331 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:14:24,332 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:14:24,450 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:14:24,451 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:14:24,454 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 11:19:26,313 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 11:19:26,313 - utils.cache - INFO - 所有缓存已清除
2025-07-16 11:19:26,317 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 11:19:26,465 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:19:26,465 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:19:26,466 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 11:19:26,581 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:19:26,581 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:19:26,582 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 11:19:26,696 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:19:26,696 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:19:26,697 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 11:19:26,865 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:19:26,866 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:19:26,993 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:19:26,994 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:19:27,160 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:19:27,161 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:19:27,294 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:19:27,295 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:19:27,429 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:19:27,430 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:19:27,551 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:19:27,553 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:19:27,564 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
2025-07-16 11:24:27,583 - core.monitor_service - INFO - 开始完全刷新缓存...
2025-07-16 11:24:27,583 - utils.cache - INFO - 所有缓存已清除
2025-07-16 11:24:27,587 - core.monitor_service - INFO - 所有缓存已清空
2025-07-16 11:24:27,727 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:24:27,728 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:24:27,728 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 2 个任务
2025-07-16 11:24:27,888 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:24:27,889 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:24:27,890 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 5 个任务
2025-07-16 11:24:28,009 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:24:28,009 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:24:28,010 - core.monitor_service - INFO - 任务过滤已禁用，直接返回所有 0 个任务
2025-07-16 11:24:28,126 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:24:28,127 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:24:28,245 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:24:28,246 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:24:28,369 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:24:28,370 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:24:28,492 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E5%88%98%E9%9D%92%E6%98%80 "HTTP/1.1 200 OK"
2025-07-16 11:24:28,493 - core.tapd_service - INFO - 获取 刘青昀 的任务数据成功: 2 个任务
2025-07-16 11:24:28,616 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%83%91%E5%87%AF%E9%91%AB "HTTP/1.1 200 OK"
2025-07-16 11:24:28,617 - core.tapd_service - INFO - 获取 郑凯鑫 的任务数据成功: 5 个任务
2025-07-16 11:24:28,741 - httpx - INFO - HTTP Request: GET https://api.tapd.cn/tasks?workspace_id=38183800&owner=%E9%99%88%E5%AB%9A%E5%A9%B7 "HTTP/1.1 200 OK"
2025-07-16 11:24:28,745 - core.tapd_service - INFO - 获取 陈嫚婷 的任务数据成功: 0 个任务
2025-07-16 11:24:28,761 - core.monitor_service - INFO - 缓存完全刷新完成，预加载了 5 个缓存项
