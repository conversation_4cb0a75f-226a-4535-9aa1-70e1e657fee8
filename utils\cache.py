#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据缓存管理器 - Utils层
提供统一的数据缓存服务，支持文件存储和内存缓存
"""

import json
import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import logging
from pathlib import Path


class DataCache:
    """数据缓存管理器"""
    
    def __init__(self, cache_file: str = None):
        """
        初始化缓存管理器
        
        Args:
            cache_file: 缓存文件路径，默认为data/cache/data_cache.json
        """
        self.logger = logging.getLogger(__name__)
        
        # 确定缓存文件路径
        if cache_file:
            self.cache_file = cache_file
        else:
            # 优先使用data/cache目录，兼容旧位置
            cache_dir_path = Path("data/cache/data_cache.json")
            root_dir_path = Path("data_cache.json")
            
            if cache_dir_path.parent.exists():
                self.cache_file = str(cache_dir_path)
            elif root_dir_path.exists():
                self.cache_file = str(root_dir_path)
            else:
                # 创建新的缓存文件路径
                cache_dir_path.parent.mkdir(parents=True, exist_ok=True)
                self.cache_file = str(cache_dir_path)
        
        self.cache_data = {}
        self.cache_lock = threading.Lock()
        
        # 缓存过期时间设置（秒）
        self.cache_expiry = {
            'person_tasks': 300,      # 个人任务缓存5分钟
            'process_overview': 300,  # 流程总览缓存5分钟  
            'workflow_status': 300,   # 工作流状态缓存5分钟
            'ocr_stories': 300,       # OCR需求缓存5分钟（改为5分钟统一）
            'task_details': 300,      # 任务详情缓存5分钟（改为5分钟统一）
            'config': 300            # 配置缓存5分钟（改为5分钟统一）
        }
        
        # 加载现有缓存
        self._load_cache()
        
        self.logger.info(f"数据缓存管理器初始化完成，缓存文件: {self.cache_file}")
    
    def _load_cache(self):
        """从文件加载缓存数据"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                self.cache_data = json.load(f)
                self.logger.info(f"从缓存文件加载数据: {len(self.cache_data)} 个条目")
        except FileNotFoundError:
            self.logger.info("缓存文件不存在，创建新缓存")
            self.cache_data = {}
        except json.JSONDecodeError as e:
            self.logger.error(f"缓存文件格式错误: {e}")
            self.cache_data = {}
        except Exception as e:
            self.logger.error(f"加载缓存失败: {e}")
            self.cache_data = {}
    
    def _save_cache(self):
        """保存缓存数据到文件"""
        try:
            # 确保目录存在
            cache_dir = Path(self.cache_file).parent
            cache_dir.mkdir(parents=True, exist_ok=True)
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_data, f, ensure_ascii=False, indent=2)
                
            self.logger.debug(f"缓存数据已保存到文件: {self.cache_file}")
            
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def _is_expired(self, cache_key: str, cache_type: str) -> bool:
        """检查缓存是否过期"""
        if cache_key not in self.cache_data:
            return True
        
        cache_entry = self.cache_data[cache_key]
        if 'timestamp' not in cache_entry:
            return True
        
        try:
            cache_time = datetime.fromisoformat(cache_entry['timestamp'])
            expiry_seconds = self.cache_expiry.get(cache_type, 300)
            
            if datetime.now() - cache_time > timedelta(seconds=expiry_seconds):
                return True
                
        except (ValueError, TypeError):
            return True
        
        return False
    
    def get(self, cache_key: str, cache_type: str = 'default') -> Optional[Any]:
        """
        获取缓存数据
        
        Args:
            cache_key: 缓存键
            cache_type: 缓存类型，用于确定过期时间
            
        Returns:
            缓存的数据，如果不存在或过期则返回None
        """
        with self.cache_lock:
            if self._is_expired(cache_key, cache_type):
                return None
            
            cache_entry = self.cache_data.get(cache_key, {})
            return cache_entry.get('data')
    
    def set(self, cache_key: str, data: Any, cache_type: str = 'default'):
        """
        设置缓存数据
        
        Args:
            cache_key: 缓存键
            data: 要缓存的数据
            cache_type: 缓存类型
        """
        with self.cache_lock:
            self.cache_data[cache_key] = {
                'data': data,
                'timestamp': datetime.now().isoformat(),
                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'cache_type': cache_type
            }
            
            self._save_cache()
            self.logger.debug(f"缓存数据已更新: {cache_key}")
    
    def delete(self, cache_key: str):
        """删除缓存条目"""
        with self.cache_lock:
            if cache_key in self.cache_data:
                del self.cache_data[cache_key]
                self._save_cache()
                self.logger.debug(f"缓存条目已删除: {cache_key}")
    
    def clear(self, cache_type: str = None):
        """
        清除缓存
        
        Args:
            cache_type: 指定缓存类型，如果为None则清除所有缓存
        """
        with self.cache_lock:
            if cache_type is None:
                self.cache_data.clear()
                self.logger.info("所有缓存已清除")
            else:
                keys_to_delete = [
                    key for key, value in self.cache_data.items()
                    if value.get('cache_type') == cache_type
                ]
                
                for key in keys_to_delete:
                    del self.cache_data[key]
                
                self.logger.info(f"已清除 {cache_type} 类型的缓存")
            
            self._save_cache()
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self.cache_lock:
            cache_info = {
                'total_entries': len(self.cache_data),
                'cache_file': self.cache_file,
                'cache_types': {},
                'expired_count': 0
            }
            
            for key, value in self.cache_data.items():
                cache_type = value.get('cache_type', 'unknown')
                if cache_type not in cache_info['cache_types']:
                    cache_info['cache_types'][cache_type] = 0
                cache_info['cache_types'][cache_type] += 1
                
                # 检查过期
                if self._is_expired(key, cache_type):
                    cache_info['expired_count'] += 1
            
            return cache_info
    
    def cleanup_expired(self):
        """清理过期的缓存条目"""
        with self.cache_lock:
            expired_keys = []
            
            for key, value in self.cache_data.items():
                cache_type = value.get('cache_type', 'default')
                if self._is_expired(key, cache_type):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache_data[key]
            
            if expired_keys:
                self._save_cache()
                self.logger.info(f"清理了 {len(expired_keys)} 个过期缓存条目")
    
    def refresh_all(self):
        """刷新所有缓存（标记为过期）"""
        with self.cache_lock:
            # 将所有缓存的时间戳设置为很久以前，强制过期
            old_time = datetime.now() - timedelta(days=1)
            
            for key in self.cache_data:
                self.cache_data[key]['timestamp'] = old_time.isoformat()
            
            self._save_cache()
            self.logger.info("所有缓存已刷新（标记为过期）")


# 创建全局缓存实例
data_cache = DataCache() 