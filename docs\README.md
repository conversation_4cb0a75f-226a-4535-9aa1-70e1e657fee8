# OCR任务监控看板 v2.0

**全新三层模块化架构，更好的代码组织和维护性**

OCR任务监控看板是一个现代化的单页Web应用，通过TAPD API获取项目任务和需求信息，为团队提供统一的任务管理和监控平台。

## 🔧 最新更新 (2025-01-15)

### 修复问题
- ✅ **解决文件监控重复创建问题**：修复了文件移动事件导致的重复处理，统一调度机制
- ✅ **修复任务标签功能**：统一标签数据结构，支持前端正常使用标签功能
- ✅ **简化调试日志**：大幅减少冗余日志输出，提高系统运行清晰度
- ✅ **优化文件处理逻辑**：完善防重复机制，确保文件只被处理一次

### 改进优化
- 🚀 **统一事件处理**：文件监控事件统一调度，避免重复触发
- 🎯 **标签数据结构**：统一 `type` 和 `value` 字段，兼容前端和后端
- 📝 **日志级别调整**：只保留关键日志，减少噪音
- 🔧 **异常处理增强**：完善文件处理的异常恢复机制

## 🏗️ 架构特性

### 三层模块化设计
- **🌐 App层（应用层）**：Flask Web应用和API路由
- **⚙️ Core层（业务逻辑层）**：TAPD服务、OCR服务、监控服务
- **🔧 Utils层（基础设施层）**：TAPD客户端、数据缓存、配置管理、工具函数

### 架构优势
- 🎯 **清晰的依赖关系**：app → core → utils
- 📦 **统一的命名规范**：所有文件遵循相同模式
- 🔧 **更好的可维护性**：按功能分层组织
- 🚀 **便于扩展**：新功能可以清晰定位到对应层级

## 📁 项目结构

```
ops_demo250630/
├── 🌐 app/                    # 应用层
│   ├── web_app.py            # Flask应用主模块
│   ├── api_routes.py         # API路由定义
│   ├── views.py              # 视图控制器
│   └── __init__.py           # 模块导出
├── ⚙️ core/                   # 业务逻辑层
│   ├── monitor_service.py    # 监控核心服务
│   ├── tapd_service.py       # TAPD业务服务
│   ├── ocr_service.py        # OCR处理服务
│   └── __init__.py           # 模块导出
├── 🔧 utils/                  # 基础设施层
│   ├── config_loader.py      # 配置加载器
│   ├── tapd_client.py        # TAPD API客户端
│   ├── cache.py              # 数据缓存管理
│   ├── csv_processor.py      # CSV数据处理
│   ├── helpers.py            # 工具函数
│   └── __init__.py           # 模块导出
├── 📊 data/                   # 数据目录
│   ├── cache/                # 缓存文件
│   ├── logs/                 # 日志文件
│   └── exports/              # 导出文件
├── 🌐 web/                    # Web资源
│   ├── templates/            # HTML模板
│   └── static/               # 静态资源
├── 📝 config/                 # 配置文件
│   ├── config.json           # 系统配置
│   └── config_template.json  # 配置模板
├── 📋 docs/                   # 文档目录
│   ├── README.md             # 项目文档
│   └── api_docs/             # API文档
├── main.py                   # 主入口文件（新）
├── requirements.txt          # 依赖包列表
└── 兼容性文件（保留旧入口）
    ├── start_web.py
    ├── ocr_monitor_web.py
    └── 其他旧文件...
```

## 🚀 快速开始

### 1. 环境准备

确保Python 3.7+已安装，然后安装依赖：

```bash
pip install -r requirements.txt
```

### 2. 配置系统

复制配置模板并填入真实信息：

```bash
copy config/config_template.json config/config.json
```

#### 获取TAPD API凭据

根据[TAPD API文档](https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/API%E9%85%8D%E7%BD%AE%E6%8C%87%E5%BC%95.html)：

1. 登录TAPD，进入`个人设置`
2. 在`开发者设置`中找到API配置
3. 获取`api_user`（API账号）和`api_password`（API口令）
4. **注意**: 是`api_password`不是token

编辑 `config/config.json` 文件，填入以下信息：

```json
{
  "tapd": {
    "api_user": "您的TAPD用户名", 
    "api_password": "您的TAPD API 口令",
    "workspace_id": 您的工作空间ID
  },
  "wechat": {
    "webhook_url": "企业微信机器人URL（可选）"
  },
  "ocr_validation": {
    "responsible_persons": {
      "ocr_validation": "OCR校验负责人",
      "completeness": "完备性负责人", 
      "accuracy": "准确性负责人"
    }
  },
  "web_server": {
    "host": "0.0.0.0",
    "port": 5001,
    "debug": true
  }
}
```

### 3. 启动系统

**统一启动入口（推荐）：**
```bash
python main.py
```

这个统一入口包含所有功能：
- 🌐 Web应用和API服务
- 📁 文件监控（自动处理CSV文件）
- ⏰ 任务状态监控（定时检查）
- ⚡ 即时标签变化处理（实时响应）
- 🔄 批次完成检测
- 🚀 后续任务自动创建

### 4. 访问看板

打开浏览器访问:
- 主看板：http://localhost:5001/dashboard
- 流程图：http://localhost:5001/process
- 管理页：http://localhost:5001/admin
- API文档：http://localhost:5001/api-docs

## ✨ 系统特性

### 核心功能
- 🎯 **单页应用**: 所有功能集成在一个页面，操作更加便捷
- 📊 **进度概览**: 顶部显示总体进度和任务状态统计
- ⚡ **简化状态**: 任务状态统一为三种：未开始、进行中、已完成
- 🔍 **智能筛选**: 支持按状态、负责人、关键字筛选任务
- 🚀 **批量操作**: 支持批量开始、批量完成任务
- 🔄 **实时数据**: 自动获取TAPD平台的最新任务数据
- 📱 **响应式设计**: 支持多种设备访问

### v2.0 新特性
- 🏗️ **三层模块化架构**: App → Core → Utils 清晰分层
- ⚙️ **统一配置管理**: 支持多环境配置和验证
- 🔧 **优化缓存机制**: 智能缓存策略，提升性能
- 📦 **模块化代码组织**: 更好的代码可维护性
- 🚀 **易于扩展**: 新功能开发更加便捷
- 📝 **完善日志系统**: 分级日志和文件记录

## 📡 API接口

系统提供以下RESTful API接口：

### 配置和系统
- `GET /api/config` - 获取系统配置信息
- `GET /api/health` - 健康检查
- `POST /api/cache/refresh` - 刷新缓存数据

### 任务管理
- `GET /api/person_tasks/<person>` - 获取指定人员的任务
- `GET /api/task/<task_id>` - 获取任务详情
- `POST /api/task/<task_id>/update` - 更新单个任务状态
- `POST /api/tasks/batch_update` - 批量更新任务状态

### 工作流和总览
- `GET /api/process_overview` - 获取流程总览数据
- `GET /api/workflow_status` - 获取工作流状态

### OCR功能
- `GET /api/ocr_problem_types` - 获取OCR问题类型列表
- `POST /api/task/save_tags` - 保存任务问题标签到TAPD API
- `GET /api/task/<task_id>/tags` - 从TAPD API获取任务问题标签

## 🛠️ 技术栈

### 后端技术
- **Python 3.7+**: 主要开发语言
- **Flask**: Web框架
- **httpx**: 异步HTTP客户端
- **pandas**: 数据处理
- **pydantic**: 数据验证

### 前端技术
- **HTML5 + CSS3**: 页面结构和样式
- **JavaScript (ES6+)**: 前端逻辑
- **Bootstrap**: 响应式UI框架
- **Chart.js**: 图表展示

### 集成服务
- **TAPD API**: 任务管理集成
- **企业微信**: 通知推送（可选）

## 🔧 开发和扩展

### 架构说明

1. **App层（应用层）**
   - `web_app.py`: Flask应用配置和初始化
   - `api_routes.py`: RESTful API路由定义
   - `views.py`: 页面视图控制器

2. **Core层（业务逻辑层）**
   - `monitor_service.py`: 监控核心业务逻辑
   - `tapd_service.py`: TAPD业务操作服务
   - `ocr_service.py`: OCR相关业务处理

3. **Utils层（基础设施层）**
   - `config_loader.py`: 统一配置管理
   - `tapd_client.py`: TAPD API客户端
   - `cache.py`: 数据缓存管理
   - `csv_processor.py`: CSV数据处理
   - `helpers.py`: 通用工具函数

### 添加新功能

1. **API接口**: 在 `app/api_routes.py` 添加新的路由
2. **业务逻辑**: 在 `core/` 相应服务中添加业务方法
3. **基础工具**: 在 `utils/` 添加通用工具函数
4. **前端功能**: 在 `web/static/js/` 添加前端逻辑

### 配置扩展

在 `config/config.json` 中添加新的配置项，并在 `utils/config_loader.py` 中添加相应的获取方法。

## 🐛 故障排除

### 常见问题

1. **配置错误**
   ```bash
   python main.py  # 会自动验证配置并显示错误
   ```

2. **TAPD连接问题**
   - 检查API凭据是否正确
   - 确认网络可访问 api.tapd.cn
   - 验证工作空间ID是否有权限

3. **端口占用**
   ```bash
   # 修改 config/config.json 中的端口号
   "web_server": {"port": 5002}
   ```

4. **缓存问题**
   ```bash
   # 清理缓存目录
   rm -rf data/cache/*
   ```

### 日志查看

系统日志存储在 `data/logs/` 目录：
- `app.log`: 应用运行日志
- `audit_log.json`: 用户操作审计日志

## 📈 版本历史

### v2.0 (2025-01-XX) - 三层架构重构
**重大改进**:
- 🏗️ **完整重构**: 将单体应用重构为三层模块化架构
- 📦 **目录重组**: 
  - `app/`: Flask Web应用和路由
  - `core/`: 业务逻辑服务  
  - `utils/`: 基础设施工具
  - `data/`: 数据和缓存文件
  - `web/`: 前端资源
  - `config/`: 配置文件
  - `docs/`: 项目文档
- 🚀 **新启动方式**: 推荐使用`python main.py`启动
- 🔧 **向后兼容**: 保留原启动方式，确保平滑迁移
- ⚙️ **统一配置**: 配置文件集中管理，支持验证
- 📝 **完善文档**: 更新架构说明和开发指南

**技术优化**:
- 清晰的模块职责分离
- 统一的导入和依赖管理
- 优化的缓存策略
- 完善的错误处理

### v1.x - 历史版本
- v1.5: 添加个人看板功能
- v1.0: 基础监控功能

## 📄 许可证

本项目采用内部使用许可证。

## 👥 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**注意**: 这是v2.0重构版本，采用全新的模块化架构。如遇到问题，可以暂时使用旧版本启动方式：`python start_web.py` 或 `python ocr_monitor_web.py`。 