#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utils模块 - 基础设施层
包含TAPD客户端、数据缓存、配置管理、CSV处理、工具函数等基础设施组件
"""

# 配置加载器
from .config_loader import ConfigLoader

# TAPD客户端
from .tapd_client import TAPDClient

# 数据缓存
from .cache import DataCache, data_cache

# CSV处理器
from .csv_processor import CSVProcessor, CSVOCRValidationRecord, clean_bill_no

# 工具函数
from .helpers import (
    # 人员代码管理
    generate_person_code, load_person_codes, save_person_codes, get_or_create_person_codes,
    
    # 用户操作日志
    log_user_action,
    
    # 状态格式化
    format_status, format_status_for_tapd,
    
    # 优先级格式化
    format_priority, format_priority_for_display,
    
    # 数据验证
    validate_bill_no,
    
    # 文件操作
    sanitize_filename, format_file_size,
    
    # 通用工具
    generate_unique_id, safe_json_loads, safe_json_dumps,
    format_datetime, truncate_text,
    
    # 常量
    AUDIT_LOG_FILE, PERSON_CODES_FILE
)

__all__ = [
    # 配置管理
    'ConfigLoader',
    
    # TAPD客户端
    'TAPDClient',
    
    # 数据缓存
    'DataCache', 'data_cache',
    
    # CSV处理
    'CSVProcessor', 'CSVOCRValidationRecord', 'clean_bill_no',
    
    # 人员代码管理
    'generate_person_code', 'load_person_codes', 'save_person_codes', 'get_or_create_person_codes',
    
    # 用户操作日志
    'log_user_action',
    
    # 状态格式化
    'format_status', 'format_status_for_tapd',
    
    # 优先级格式化
    'format_priority', 'format_priority_for_display',
    
    # 数据验证
    'validate_bill_no',
    
    # 文件操作
    'sanitize_filename', 'format_file_size',
    
    # 通用工具
    'generate_unique_id', 'safe_json_loads', 'safe_json_dumps',
    'format_datetime', 'truncate_text',
    
    # 常量
    'AUDIT_LOG_FILE', 'PERSON_CODES_FILE'
] 