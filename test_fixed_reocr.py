#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的重新OCR功能
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils import ConfigLoader
from core import TaskStatusMonitor, TAPDService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_fixed_reocr():
    """测试修复后的重新OCR功能"""
    print("🧪 测试修复后的重新OCR功能")
    
    # 初始化服务
    config_loader = ConfigLoader()
    task_monitor = TaskStatusMonitor(config_loader)
    tapd_service = TAPDService(config_loader)
    
    print("✅ 服务初始化完成")
    
    # 使用已知的需求和任务
    story_id = "1138183800001000907"
    original_task_id = "1138183800001000914"  # 准确性问题-租赁合同
    
    print(f"🎯 测试需求: {story_id}")
    print(f"📝 测试任务: {original_task_id}")
    
    # 1. 模拟添加标签创建后续任务
    print(f"\n⚡ 模拟为任务添加标签...")
    test_labels = ["accuracy_format_error"]  # 格式识别错误
    print(f"🏷️  添加标签: {test_labels}")
    
    # 触发标签添加事件
    await task_monitor.handle_task_tag_added(original_task_id, test_labels)
    
    print("✅ 标签添加处理完成")
    
    # 2. 等待一段时间后检查是否创建了后续任务
    print(f"\n⏰ 等待5秒后检查后续任务...")
    time.sleep(5)
    
    story_tasks = await tapd_service.get_story_tasks(story_id)
    print(f"📋 需求 {story_id} 下共有 {len(story_tasks)} 个任务:")
    
    followup_tasks = []
    for task in story_tasks:
        task_name = task.get('name', '')
        task_id = task.get('id', '')
        task_status = task.get('status', '')
        print(f"  - {task_name} (状态: {task_status}, ID: {task_id})")
        
        # 检查是否是后续任务
        followup_rules = config_loader.get_ocr_config().get("followup_rules", [])
        followup_task_titles = [rule.get('new_task_title', '') for rule in followup_rules]
        if any(title in task_name for title in followup_task_titles if title):
            followup_tasks.append(task)
            print(f"    ✅ 这是后续任务")
    
    print(f"\n📊 统计: 找到 {len(followup_tasks)} 个后续任务")
    
    if followup_tasks:
        print(f"🎉 成功创建了后续任务!")
        
        # 3. 模拟完成所有任务
        print(f"\n⚡ 模拟完成所有任务...")
        
        # 完成后续任务
        for followup_task in followup_tasks:
            followup_task_id = followup_task.get('id')
            task_name = followup_task.get('name', '')
            current_status = followup_task.get('status', '')
            
            if current_status != '已完成':
                print(f"📝 完成后续任务: {task_name}")
                result = await tapd_service.update_task_status(followup_task_id, "已完成")
                print(f"  结果: {result}")
        
        # 4. 触发批次完成检查
        print(f"\n⚡ 触发批次完成检查...")
        await task_monitor._check_single_batch_completion(story_id)
        
        # 5. 等待一段时间后检查是否创建了重新OCR任务
        print(f"\n⏰ 等待5秒后检查重新OCR任务...")
        time.sleep(5)
        
        final_story_tasks = await tapd_service.get_story_tasks(story_id)
        print(f"📋 最终需求 {story_id} 下共有 {len(final_story_tasks)} 个任务:")
        
        reocr_tasks = []
        for task in final_story_tasks:
            task_name = task.get('name', '')
            task_id = task.get('id', '')
            task_status = task.get('status', '')
            print(f"  - {task_name} (状态: {task_status}, ID: {task_id})")
            
            if '重新OCR' in task_name:
                reocr_tasks.append(task)
                print(f"    🎉 这是重新OCR任务!")
        
        if reocr_tasks:
            print(f"\n🎉 成功! 创建了 {len(reocr_tasks)} 个重新OCR任务")
            for task in reocr_tasks:
                print(f"  ✅ {task.get('name', '')} (负责人: {task.get('owner', '')})")
        else:
            print(f"\n❌ 没有创建重新OCR任务")
    else:
        print(f"\n❌ 没有创建后续任务")
    
    print("\n✅ 测试完成")

if __name__ == '__main__':
    asyncio.run(test_fixed_reocr())
