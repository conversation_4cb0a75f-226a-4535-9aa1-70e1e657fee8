#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块 - Utils层
包含各种通用的工具函数和辅助功能
"""

import json
import logging
import hashlib
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import request


# 审计日志文件路径
AUDIT_LOG_FILE = 'data/logs/audit_log.json'
PERSON_CODES_FILE = 'data/cache/person_codes.json'


def generate_person_code(person_name: str) -> str:
    """为负责人生成唯一代码"""
    hash_obj = hashlib.md5(person_name.encode('utf-8'))
    return hash_obj.hexdigest()[:8]


def load_person_codes() -> Dict[str, str]:
    """加载负责人代码映射"""
    try:
        with open(PERSON_CODES_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}
    except Exception as e:
        logging.error(f"加载负责人代码失败: {e}")
        return {}


def save_person_codes(codes: Dict[str, str]):
    """保存负责人代码映射"""
    try:
        # 确保目录存在
        import os
        os.makedirs(os.path.dirname(PERSON_CODES_FILE), exist_ok=True)
        
        with open(PERSON_CODES_FILE, 'w', encoding='utf-8') as f:
            json.dump(codes, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.error(f"保存负责人代码失败: {e}")


def get_or_create_person_codes(responsible_persons: Dict[str, str]) -> Dict[str, Dict[str, str]]:
    """获取或创建负责人代码映射"""
    # 获取所有人员列表
    all_persons = set()
    
    # 从配置获取人员
    all_persons.update(responsible_persons.values())
    
    # 添加已知的工作流人员
    workflow_persons = ['陈嫚婷', '郑凯鑫', '刘青昀', '李玲', '司马维维']
    all_persons.update(workflow_persons)
    
    existing_codes = load_person_codes()
    
    # 添加已有代码中的人员
    all_persons.update(existing_codes.keys())
    
    # 去除空值
    all_persons = {person for person in all_persons if person and person.strip()}
    
    person_codes = {}
    code_to_person = {}
    codes_updated = False
    
    for person in all_persons:
        if person not in existing_codes:
            # 为新的人员生成代码
            new_code = generate_person_code(person)
            # 确保代码唯一性
            while new_code in code_to_person:
                new_code = generate_person_code(f"{person}_{uuid.uuid4().hex[:4]}")
            existing_codes[person] = new_code
            codes_updated = True
            logging.info(f"为 {person} 生成新代码: {new_code}")
        
        person_codes[person] = existing_codes[person]
        code_to_person[existing_codes[person]] = person
    
    # 如果有更新，保存代码
    if codes_updated:
        save_person_codes(existing_codes)
    
    return {
        'person_to_code': person_codes,
        'code_to_person': code_to_person
    }


def log_user_action(person_code: str, person_name: str, action: str, details: Dict[str, Any] = None):
    """记录用户操作用于审计"""
    try:
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'person_code': person_code,
            'person_name': person_name,
            'action': action,
            'details': details or {},
            'ip_address': request.remote_addr if request else 'unknown',
            'user_agent': request.headers.get('User-Agent', 'unknown') if request else 'unknown'
        }
        
        # 确保日志目录存在
        import os
        os.makedirs(os.path.dirname(AUDIT_LOG_FILE), exist_ok=True)
        
        # 读取现有日志
        try:
            with open(AUDIT_LOG_FILE, 'r', encoding='utf-8') as f:
                logs = json.load(f)
        except FileNotFoundError:
            logs = []
        
        logs.append(log_entry)
        
        # 保持最近1000条记录
        if len(logs) > 1000:
            logs = logs[-1000:]
        
        # 保存日志
        with open(AUDIT_LOG_FILE, 'w', encoding='utf-8') as f:
            json.dump(logs, f, ensure_ascii=False, indent=2)
            
        logging.info(f"记录用户操作: {person_name}({person_code}) - {action}")
        
    except Exception as e:
        logging.error(f"记录用户操作失败: {e}")


def format_status(status: str) -> str:
    """将TAPD状态格式化为前端显示格式"""
    # 处理空值情况
    if not status or status in [None, 'None', 'null', '']:
        logging.debug(f"状态格式化：空状态 '{status}' -> '未开始'")
        return '未开始'
        
    # TAPD标准状态映射到前端中文显示
    tapd_status_map = {
        'open': '未开始',
        'progressing': '进行中', 
        'done': '已完成'
    }
    
    # 兼容中文状态（TAPD返回的可能是中文）
    chinese_status_map = {
        '未开始': '未开始',
        '进行中': '进行中',
        '已完成': '已完成'
    }
    
    # 先尝试TAPD标准状态，再尝试中文状态
    status_str = str(status).strip()
    formatted_status = tapd_status_map.get(status_str.lower())
    if not formatted_status:
        formatted_status = chinese_status_map.get(status_str, '未开始')
    
    logging.debug(f"状态格式化：'{status}' -> '{formatted_status}'")    
    return formatted_status


def format_status_for_tapd(frontend_status: str) -> str:
    """将前端状态格式化为TAPD标准状态"""
    # 前端中文状态映射到TAPD标准状态
    frontend_to_tapd_map = {
        '未开始': 'open',
        '进行中': 'progressing', 
        '已完成': 'done',
        # 兼容旧格式
        'pending': 'open',
        'processing': 'progressing',
        'completed': 'done'
    }
    return frontend_to_tapd_map.get(frontend_status, 'open')


def format_priority(priority: str) -> str:
    """格式化优先级"""
    priority_map = {
        '紧急': 'high',
        '高': 'high', 
        '中': 'medium',
        '低': 'low'
    }
    return priority_map.get(priority, 'medium')


def format_priority_for_display(priority: str) -> str:
    """将优先级格式化为显示格式"""
    display_map = {
        'high': '高',
        'medium': '中', 
        'low': '低',
        '高': '高',
        '中': '中',
        '低': '低'
    }
    return display_map.get(priority, '中')


def validate_bill_no(bill_no: str) -> bool:
    """验证借据号格式"""
    if not bill_no or not bill_no.strip():
        return False
    
    # 基本格式检查（可根据实际业务规则调整）
    bill_no = str(bill_no).strip()
    return len(bill_no) >= 8 and bill_no.isdigit()


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除非法字符"""
    import re
    # 移除或替换非法字符
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    return sanitized.strip()


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def generate_unique_id() -> str:
    """生成唯一ID"""
    return str(uuid.uuid4())


def safe_json_loads(json_str: str, default=None):
    """安全的JSON解析"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default or {}


def safe_json_dumps(obj: Any, default="{}") -> str:
    """安全的JSON序列化"""
    try:
        return json.dumps(obj, ensure_ascii=False, indent=2)
    except (TypeError, ValueError):
        return default


def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化日期时间"""
    if isinstance(dt, str):
        try:
            dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
        except ValueError:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime(format_str)
    
    return str(dt)


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """截断文本"""
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix 