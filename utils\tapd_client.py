#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TAPD API客户端 - Utils层
统一的TAPD API接口封装，提供基础的HTTP API调用功能
"""

import asyncio
import json
import logging
import base64
from typing import Dict, Any, Optional, List
from datetime import datetime
import httpx

from .config_loader import ConfigLoader


class TAPDClient:
    """TAPD API客户端"""
    
    def __init__(self, config_loader: Optional[ConfigLoader] = None):
        """
        初始化TAPD客户端
        
        Args:
            config_loader: 配置加载器实例
        """
        self.logger = logging.getLogger(__name__)
        
        # 获取配置
        self.config_loader = config_loader or ConfigLoader()
        self.config = self.config_loader.get_tapd_config()
        
        # TAPD API配置
        self.api_base_url = "https://api.tapd.cn"
        self.base_url = "https://www.tapd.cn"
        
        # 认证信息
        self.auth_header = None
        self._setup_auth()
        
        self.logger.info("TAPD客户端初始化完成")
    
    def _setup_auth(self):
        """设置认证信息"""
        api_user = self.config.get("api_user")
        api_password = self.config.get("api_password")
        
        if api_user and api_password:
            credentials = f"{api_user}:{api_password}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            self.auth_header = f"Basic {encoded_credentials}"
            self.logger.info(f"TAPD认证配置完成: {api_user}")
        else:
            self.logger.error("TAPD认证信息缺失")
            raise ValueError("TAPD认证信息缺失")
    
    async def create_story(self, workspace_id: int, name: str, description: str = "", 
                          priority: str = "中", status: str = "新建", parent_id: Optional[str] = None, 
                          owner: str = "") -> Dict[str, Any]:
        """创建需求"""
        url = f"{self.api_base_url}/stories"
        
        data = {
            "workspace_id": workspace_id,
            "name": name,
            "description": description,
            "priority": priority,
            "status": status,
            "owner": owner
        }
        
        if parent_id:
            data["parent_id"] = parent_id
        
        headers = {
            "Authorization": self.auth_header,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, data=data, headers=headers)
                response.raise_for_status()
                result = response.json()
                
                if result.get("status") == 1:
                    self.logger.info(f"需求创建成功: {name}")
                    return result
                else:
                    self.logger.error(f"需求创建失败: {result}")
                    return {"status": "error", "message": result.get("info", "未知错误")}
                    
        except Exception as e:
            self.logger.error(f"创建需求异常: {e}")
            return {"status": "error", "message": str(e)}
    
    async def create_task(self, workspace_id: int, name: str, description: str = "", 
                         story_id: str = None, owner: str = "", priority: str = "中", 
                         begin: str = None, due: str = None, cc: str = None, 
                         status: str = None) -> Dict[str, Any]:
        """创建任务"""
        url = f"{self.api_base_url}/tasks"
        
        data = {
            "workspace_id": workspace_id,
            "name": name,
            "description": description,
            "owner": owner,
            "priority": priority
        }
        
        if story_id:
            data["story_id"] = story_id
        if begin:
            data["begin"] = begin
        if due:
            data["due"] = due
        if cc:
            data["cc"] = cc
        if status:
            data["status"] = status
        
        headers = {
            "Authorization": self.auth_header,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, data=data, headers=headers)
                response.raise_for_status()
                result = response.json()
                
                if result.get("status") == 1:
                    self.logger.info(f"任务创建成功: {name}")
                    return result
                else:
                    self.logger.error(f"任务创建失败: {result}")
                    return {"status": "error", "message": result.get("info", "未知错误")}
                    
        except Exception as e:
            self.logger.error(f"创建任务异常: {e}")
            return {"status": "error", "message": str(e)}
    
    async def get_tasks(self, workspace_id: int, **kwargs) -> Dict[str, Any]:
        """获取任务列表"""
        url = f"{self.api_base_url}/tasks"
        
        params = {"workspace_id": workspace_id}
        params.update(kwargs)
        
        headers = {"Authorization": self.auth_header}
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, headers=headers)
                response.raise_for_status()
                result = response.json()
                
                if result.get("status") == 1:
                    return result
                else:
                    self.logger.error(f"获取任务失败: {result}")
                    return {"status": "error", "message": result.get("info", "未知错误")}
                    
        except Exception as e:
            self.logger.error(f"获取任务异常: {e}")
            return {"status": "error", "message": str(e)}
    
    async def update_task(self, workspace_id: int, task_id: str, **update_fields) -> Dict[str, Any]:
        """更新任务"""
        url = f"{self.api_base_url}/tasks"
        
        data = {
            "workspace_id": workspace_id,
            "id": task_id
        }
        data.update(update_fields)
        
        headers = {
            "Authorization": self.auth_header,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, data=data, headers=headers)
                response.raise_for_status()
                result = response.json()
                
                if result.get("status") == 1:
                    self.logger.info(f"任务更新成功: {task_id}")
                    return result
                else:
                    self.logger.error(f"任务更新失败: {result}")
                    return {"status": "error", "message": result.get("info", "未知错误")}
                    
        except Exception as e:
            self.logger.error(f"更新任务异常: {e}")
            return {"status": "error", "message": str(e)}
    
    async def get_task(self, workspace_id: int, task_id: str) -> Dict[str, Any]:
        """获取单个任务详情"""
        url = f"{self.api_base_url}/tasks"
        
        params = {
            "workspace_id": workspace_id,
            "id": task_id
        }
        
        headers = {"Authorization": self.auth_header}
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, headers=headers)
                response.raise_for_status()
                result = response.json()
                
                if result.get("status") == 1:
                    return result
                else:
                    self.logger.error(f"获取任务详情失败: {result}")
                    return {"status": "error", "message": result.get("info", "未知错误")}
                    
        except Exception as e:
            self.logger.error(f"获取任务详情异常: {e}")
            return {"status": "error", "message": str(e)}
    
    async def get_task_fields(self, workspace_id: int) -> Dict[str, Any]:
        """
        获取任务字段配置信息
        
        Args:
            workspace_id: 工作空间ID
            
        Returns:
            任务字段配置
        """
        if not self.auth_header:
            return {"error": "TAPD API认证配置不完整"}
        
        try:
            url = f"{self.api_base_url}/task_fields_settings"
            
            params = {
                "workspace_id": workspace_id
            }
            
            headers = {
                "Authorization": self.auth_header
            }
            
            self.logger.info(f"获取工作空间 {workspace_id} 的任务字段配置")
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, params=params, headers=headers)
                
                if response.status_code == 200:
                    result = response.json()
                    self.logger.info("任务字段配置获取成功")
                    return {
                        "success": True,
                        "data": result
                    }
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text}"
                    self.logger.error(f"获取任务字段配置失败: {error_msg}")
                    return {
                        "error": error_msg,
                        "workspace_id": workspace_id
                    }
                    
        except Exception as e:
            self.logger.error(f"获取任务字段配置异常: {e}")
            return {
                "error": f"API调用异常: {str(e)}",
                "workspace_id": workspace_id
            } 