#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由模块 - App层
定义所有RESTful API路由
"""

import logging
import asyncio
from datetime import datetime
from flask import Flask, jsonify, request, current_app

from app.web_app import get_monitor_service, get_config_loader


def register_api_routes(app: Flask):
    """注册所有API路由"""
    
    @app.route('/api/config', methods=['GET'])
    def get_config():
        """获取系统配置信息"""
        try:
            monitor_service = get_monitor_service(current_app)
            config_data = monitor_service.get_config_data()
            
            if config_data.get("success"):
                return jsonify(config_data), 200
            else:
                return jsonify(config_data), 500
                
        except Exception as e:
            app.logger.error(f"获取配置失败: {e}")
            return jsonify({"error": "获取配置失败", "message": str(e)}), 500
    
    @app.route('/api/person_tasks/<person>', methods=['GET'])
    def get_person_tasks(person):
        """获取指定人员的任务（按角色查询）"""
        try:
            monitor_service = get_monitor_service(current_app)
            config_loader = get_config_loader(current_app)
            
            # 根据人名找到对应的角色
            responsible_persons = config_loader.get_responsible_persons()
            role_mapping = config_loader.get_role_mapping()
            
            # 查找该人员对应的角色
            person_role = None
            for role, actual_person in responsible_persons.items():
                if actual_person == person:
                    person_role = role
                    break
            
            if not person_role:
                app.logger.warning(f"未找到人员 '{person}' 对应的角色")
                return jsonify({"error": "未找到对应角色", "message": f"人员 {person} 未配置角色"}), 404
            
            # 获取角色名称
            role_name = role_mapping.get(person_role, person_role)
            
            app.logger.info(f"查询人员 '{person}' 的任务，对应角色: {person_role}, 角色名称: {role_name}")
            
            # 运行异步函数，按角色名称查询任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(monitor_service.get_person_tasks(role_name))
            finally:
                loop.close()
            
            if result.get("success"):
                return jsonify(result), 200
            else:
                return jsonify(result), 500
                
        except Exception as e:
            app.logger.error(f"获取人员任务失败: {e}")
            return jsonify({"error": "获取人员任务失败", "message": str(e)}), 500
    
    @app.route('/api/available_persons', methods=['GET'])
    def get_available_persons():
        """获取可用的人员页面列表"""
        try:
            config_loader = get_config_loader(current_app)
            responsible_persons = config_loader.get_responsible_persons()
            
            # 获取人员代码映射
            from utils import get_or_create_person_codes
            person_codes = get_or_create_person_codes(responsible_persons)
            person_to_code = person_codes.get('person_to_code', {})
            
            # 构造人员列表
            persons = []
            for person_name, person_code in person_to_code.items():
                persons.append({
                    'name': person_name,
                    'code': person_code
                })
            
            # 按姓名排序
            persons.sort(key=lambda x: x['name'])
            
            return jsonify({
                'success': True,
                'persons': persons,
                'total': len(persons)
            }), 200
            
        except Exception as e:
            app.logger.error(f"获取可用人员列表失败: {e}")
            return jsonify({"error": "获取可用人员列表失败", "message": str(e)}), 500
    
    @app.route('/api/person_codes', methods=['GET'])
    def get_person_codes():
        """获取负责人代码映射"""
        try:
            config_loader = get_config_loader(current_app)
            responsible_persons = config_loader.get_responsible_persons()
            
            from utils import get_or_create_person_codes
            person_codes = get_or_create_person_codes(responsible_persons)
            
            return jsonify(person_codes), 200
            
        except Exception as e:
            app.logger.error(f"获取负责人代码失败: {e}")
            return jsonify({"error": "获取负责人代码失败", "message": str(e)}), 500
    
    @app.route('/api/file_monitor/status', methods=['GET'])
    def get_file_monitor_status():
        """获取文件监控状态"""
        try:
            # 检查是否有文件监控服务实例
            file_monitor = getattr(current_app, 'file_monitor', None)
            
            if file_monitor:
                status = file_monitor.get_monitoring_status()
                return jsonify({
                    "success": True,
                    "status": status
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "message": "文件监控服务未启用"
                }), 200
                
        except Exception as e:
            app.logger.error(f"获取文件监控状态失败: {e}")
            return jsonify({"error": "获取文件监控状态失败", "message": str(e)}), 500
    
    @app.route('/api/file_monitor/start', methods=['POST'])
    def start_file_monitor():
        """启动文件监控"""
        try:
            file_monitor = getattr(current_app, 'file_monitor', None)
            
            if not file_monitor:
                return jsonify({
                    "success": False,
                    "message": "文件监控服务未初始化"
                }), 400
            
            file_monitor.start_monitoring()
            
            return jsonify({
                "success": True,
                "message": "文件监控已启动"
            }), 200
            
        except Exception as e:
            app.logger.error(f"启动文件监控失败: {e}")
            return jsonify({"error": "启动文件监控失败", "message": str(e)}), 500
    
    @app.route('/api/file_monitor/stop', methods=['POST'])
    def stop_file_monitor():
        """停止文件监控"""
        try:
            file_monitor = getattr(current_app, 'file_monitor', None)
            
            if not file_monitor:
                return jsonify({
                    "success": False,
                    "message": "文件监控服务未初始化"
                }), 400
            
            file_monitor.stop_monitoring()
            
            return jsonify({
                "success": True,
                "message": "文件监控已停止"
            }), 200
            
        except Exception as e:
            app.logger.error(f"停止文件监控失败: {e}")
            return jsonify({"error": "停止文件监控失败", "message": str(e)}), 500

    @app.route('/api/process_overview', methods=['GET'])
    def get_process_overview():
        """获取流程总览数据"""
        try:
            monitor_service = get_monitor_service(current_app)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(monitor_service.get_process_overview())
            finally:
                loop.close()
            
            if result.get("success"):
                return jsonify(result), 200
            else:
                return jsonify(result), 500
                
        except Exception as e:
            app.logger.error(f"获取流程总览失败: {e}")
            return jsonify({"error": "获取流程总览失败", "message": str(e)}), 500
    
    @app.route('/api/workflow_status', methods=['GET'])
    def get_workflow_status():
        """获取工作流状态"""
        try:
            monitor_service = get_monitor_service(current_app)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(monitor_service.get_workflow_status())
            finally:
                loop.close()
            
            if result.get("success"):
                return jsonify(result), 200
            else:
                return jsonify(result), 500
                
        except Exception as e:
            app.logger.error(f"获取工作流状态失败: {e}")
            return jsonify({"error": "获取工作流状态失败", "message": str(e)}), 500
    
    @app.route('/api/task/<task_id>', methods=['GET'])
    def get_task_details(task_id):
        """获取任务详情"""
        try:
            monitor_service = get_monitor_service(current_app)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(monitor_service.get_task_details(task_id))
            finally:
                loop.close()
            
            if result.get("success"):
                return jsonify(result), 200
            else:
                return jsonify(result), 500
                
        except Exception as e:
            app.logger.error(f"获取任务详情失败: {e}")
            return jsonify({"error": "获取任务详情失败", "message": str(e)}), 500
    
    @app.route('/api/task/<task_id>/update', methods=['POST'])
    def update_task_status(task_id):
        """更新单个任务状态"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "请求数据格式错误"}), 400
            
            new_status = data.get('status')
            if not new_status:
                return jsonify({"error": "缺少状态参数"}), 400
            
            # 获取操作人信息（可选）
            person_code = data.get('person_code', '')
            person_name = data.get('person_name', '')
            
            monitor_service = get_monitor_service(current_app)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    monitor_service.update_task_status(task_id, new_status, person_code, person_name)
                )
            finally:
                loop.close()

            if result.get("success"):
                # 任务状态更新成功后，触发批次完成检查
                try:
                    task_status_monitor = getattr(current_app, 'task_status_monitor', None)
                    if task_status_monitor and new_status == '已完成':
                        # 获取任务详情以获取story_id
                        loop2 = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop2)
                        try:
                            task_detail_result = loop2.run_until_complete(
                                monitor_service.get_task_details(task_id)
                            )
                            if task_detail_result.get("success"):
                                task_data = task_detail_result.get("task", {})
                                story_id = task_data.get("story_id")
                                if story_id:
                                    loop2.run_until_complete(
                                        task_status_monitor.handle_task_status_changed(task_id, new_status, story_id)
                                    )
                                    app.logger.info(f"已触发任务 {task_id} 的批次完成检查")
                        finally:
                            loop2.close()
                except Exception as e:
                    app.logger.error(f"触发批次完成检查失败: {e}")
                    # 不影响任务状态更新的成功返回
                
                return jsonify(result), 200
            else:
                return jsonify(result), 500

        except Exception as e:
            app.logger.error(f"更新任务状态失败: {e}")
            return jsonify({"error": "更新任务状态失败", "message": str(e)}), 500
    
    @app.route('/api/tasks/batch_update', methods=['POST'])
    def batch_update_tasks():
        """批量更新任务状态"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "请求数据格式错误"}), 400
            
            task_updates = data.get('tasks', [])
            if not task_updates:
                return jsonify({"error": "缺少任务更新数据"}), 400
            
            # 获取操作人信息（可选）
            person_code = data.get('person_code', '')
            person_name = data.get('person_name', '')
            
            monitor_service = get_monitor_service(current_app)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    monitor_service.batch_update_tasks(task_updates, person_code, person_name)
                )
            finally:
                loop.close()
            
            return jsonify(result), 200
                
        except Exception as e:
            app.logger.error(f"批量更新任务失败: {e}")
            return jsonify({"error": "批量更新任务失败", "message": str(e)}), 500
    
    @app.route('/api/task/save_tags', methods=['POST'])
    def save_task_tags():
        """保存任务问题标签"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "请求数据格式错误"}), 400
            
            task_id = data.get('task_id')
            tags = data.get('problem_tags', [])  # 前端发送的字段名是problem_tags
            
            if not task_id:
                return jsonify({"error": "缺少任务ID"}), 400
            
            # 获取操作人信息（可选）
            person_code = data.get('person_code', '')
            person_name = data.get('person_name', '')
            
            monitor_service = get_monitor_service(current_app)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    monitor_service.save_task_tags(task_id, tags, person_code, person_name)
                )
            finally:
                loop.close()
            
            if result.get("success"):
                # 标签保存成功后，检查是否需要创建后续任务
                try:
                    # 获取任务状态监控服务
                    task_status_monitor = getattr(current_app, 'task_status_monitor', None)

                    if task_status_monitor and tags:
                        app.logger.info(f"任务 {task_id} 添加了标签 {tags}，检查是否需要创建后续任务")

                        # 异步处理标签添加事件
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            loop.run_until_complete(task_status_monitor.handle_task_tag_added(task_id, tags))
                        finally:
                            loop.close()

                        app.logger.info(f"已处理任务 {task_id} 的标签添加事件")

                except Exception as e:
                    app.logger.error(f"创建后续任务失败: {e}")
                    # 不影响标签保存的成功返回

                # 返回格式与前端期望一致
                return jsonify({
                    "success": True,
                    "message": result.get("message", "标签保存成功"),
                    "tags_count": len(tags)
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "error": result.get("message", "保存失败")
                }), 500
                
        except Exception as e:
            app.logger.error(f"保存任务标签失败: {e}")
            return jsonify({"error": "保存任务标签失败", "message": str(e)}), 500
    
    @app.route('/api/task/<task_id>/tags', methods=['GET'])
    def get_task_tags(task_id):
        """获取任务问题标签"""
        try:
            monitor_service = get_monitor_service(current_app)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(monitor_service.get_task_tags(task_id))
            finally:
                loop.close()
            
            if result.get("success"):
                # 返回格式与前端期望一致
                return jsonify({
                    "success": True,
                    "problem_tags": result.get("tags", []),  # 前端期望的字段名是problem_tags
                    "label_string": result.get("label_string", ""),
                    "last_updated": result.get("updated_at", "")
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "error": result.get("message", "获取标签失败")
                }), 500
                
        except Exception as e:
            app.logger.error(f"获取任务标签失败: {e}")
            return jsonify({"error": "获取任务标签失败", "message": str(e)}), 500

    @app.route('/api/task/batch_save_tags', methods=['POST'])
    def batch_save_task_tags():
        """批量保存任务问题标签"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "请求数据格式错误"}), 400

            task_ids = data.get('task_ids', [])
            problem_tag = data.get('problem_tag', '')
            replace_existing = data.get('replace_existing', False)

            if not task_ids:
                return jsonify({"error": "缺少任务ID列表"}), 400

            if not problem_tag:
                return jsonify({"error": "缺少问题标签"}), 400

            # 获取操作人信息（可选）
            person_code = data.get('person_code', '')
            person_name = data.get('person_name', '')

            monitor_service = get_monitor_service(current_app)
            task_status_monitor = getattr(current_app, 'task_status_monitor', None)

            success_count = 0
            failure_count = 0
            created_followup_tasks = []

            async def process_batch():
                for task_id in task_ids:
                    try:
                        # 获取现有标签
                        if replace_existing:
                            new_tags = [problem_tag]
                        else:
                            existing_result = await monitor_service.get_task_tags(task_id)
                            existing_tags = existing_result.get("tags", []) if existing_result.get("success") else []
                            new_tags = list(set(existing_tags + [problem_tag]))  # 去重

                        # 保存标签
                        result = await monitor_service.save_task_tags(task_id, new_tags, person_code, person_name)

                        if result.get("success"):
                            nonlocal success_count
                            success_count += 1

                            # 检查是否需要创建后续任务
                            if task_status_monitor:
                                try:
                                    app.logger.info(f"批量操作：检测到任务 {task_id} 添加了标签，检查是否需要创建后续任务")
                                    await task_status_monitor.handle_task_tag_added(task_id, [problem_tag])
                                    created_followup_tasks.append(task_id)

                                except Exception as e:
                                    app.logger.error(f"为任务 {task_id} 创建后续任务失败: {e}")
                        else:
                            nonlocal failure_count
                            failure_count += 1
                            app.logger.error(f"保存任务 {task_id} 标签失败: {result.get('message')}")

                    except Exception as e:
                        failure_count += 1
                        app.logger.error(f"处理任务 {task_id} 失败: {e}")

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(process_batch())
            finally:
                loop.close()

            # 返回结果
            message = f"批量标记完成：成功 {success_count} 个，失败 {failure_count} 个"
            if created_followup_tasks:
                message += f"，为 {len(created_followup_tasks)} 个已完成任务创建了后续任务"

            return jsonify({
                "success": True,
                "message": message,
                "success_count": success_count,
                "failure_count": failure_count,
                "created_followup_tasks": len(created_followup_tasks)
            }), 200

        except Exception as e:
            app.logger.error(f"批量保存任务标签失败: {e}")
            return jsonify({"error": "批量保存任务标签失败", "message": str(e)}), 500

    @app.route('/api/ocr_problem_types', methods=['GET'])
    def get_ocr_problem_types():
        """获取OCR问题类型列表"""
        try:
            monitor_service = get_monitor_service(current_app)
            problem_types = monitor_service.get_ocr_problem_types()
            
            return jsonify({
                "success": True,
                "problem_types": problem_types
            }), 200
                
        except Exception as e:
            app.logger.error(f"获取OCR问题类型失败: {e}")
            return jsonify({"error": "获取OCR问题类型失败", "message": str(e)}), 500
    
    @app.route('/api/cache/refresh', methods=['POST'])
    def refresh_cache():
        """刷新缓存数据"""
        try:
            monitor_service = get_monitor_service(current_app)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(monitor_service.refresh_cache())
            finally:
                loop.close()
            
            if result.get("success"):
                return jsonify(result), 200
            else:
                return jsonify(result), 500
                
        except Exception as e:
            app.logger.error(f"刷新缓存失败: {e}")
            return jsonify({"error": "刷新缓存失败", "message": str(e)}), 500

    @app.route('/api/task_monitor/status', methods=['GET'])
    def get_task_monitor_status():
        """获取任务状态监控状态"""
        try:
            # 获取任务状态监控服务
            task_status_monitor = getattr(current_app, 'task_status_monitor', None)

            if not task_status_monitor:
                return jsonify({
                    "success": False,
                    "message": "任务状态监控服务未初始化"
                }), 500

            status_info = task_status_monitor.get_monitor_status()

            return jsonify({
                "success": True,
                "monitor_status": status_info,
                "timestamp": datetime.now().isoformat()
            }), 200

        except Exception as e:
            app.logger.error(f"获取任务监控状态失败: {e}")
            return jsonify({
                "success": False,
                "error": "获取任务监控状态失败",
                "message": str(e)
            }), 500

    @app.route('/api/task_monitor/start', methods=['POST'])
    def start_task_monitor():
        """启动任务状态监控"""
        try:
            # 获取任务状态监控服务
            task_status_monitor = getattr(current_app, 'task_status_monitor', None)

            if not task_status_monitor:
                return jsonify({
                    "success": False,
                    "message": "任务状态监控服务未初始化"
                }), 500

            if task_status_monitor.is_monitoring:
                return jsonify({
                    "success": True,
                    "message": "任务状态监控已在运行"
                }), 200

            success = task_status_monitor.start_monitoring()

            if success:
                return jsonify({
                    "success": True,
                    "message": "任务状态监控启动成功"
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "message": "任务状态监控启动失败"
                }), 500

        except Exception as e:
            app.logger.error(f"启动任务监控失败: {e}")
            return jsonify({
                "success": False,
                "error": "启动任务监控失败",
                "message": str(e)
            }), 500

    @app.route('/api/task_monitor/stop', methods=['POST'])
    def stop_task_monitor():
        """停止任务状态监控"""
        try:
            # 获取任务状态监控服务
            task_status_monitor = getattr(current_app, 'task_status_monitor', None)

            if not task_status_monitor:
                return jsonify({
                    "success": False,
                    "message": "任务状态监控服务未初始化"
                }), 500

            if not task_status_monitor.is_monitoring:
                return jsonify({
                    "success": True,
                    "message": "任务状态监控已停止"
                }), 200

            task_status_monitor.stop_monitoring()

            return jsonify({
                "success": True,
                "message": "任务状态监控停止成功"
            }), 200

        except Exception as e:
            app.logger.error(f"停止任务监控失败: {e}")
            return jsonify({
                "success": False,
                "error": "停止任务监控失败",
                "message": str(e)
            }), 500
    
    @app.route('/api/tapd/task_fields', methods=['GET'])
    def get_tapd_task_fields():
        """获取TAPD任务字段配置，包括状态和优先级选项"""
        try:
            monitor_service = get_monitor_service(current_app)
            
            # 运行异步函数获取字段配置
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(monitor_service.get_tapd_task_fields())
            finally:
                loop.close()
            
            return jsonify(result), 200
                
        except Exception as e:
            app.logger.error(f"获取TAPD任务字段配置失败: {e}")
            # 返回备用配置，确保前端能正常工作
            return jsonify({
                'success': True,
                'status_options': [
                    {'value': 'open', 'label': '未开始', 'tapd_value': 'open'},
                    {'value': 'progressing', 'label': '进行中', 'tapd_value': 'progressing'},
                    {'value': 'done', 'label': '已完成', 'tapd_value': 'done'}
                ],
                'priority_options': [
                    {'value': '高', 'label': '高', 'tapd_value': '高'},
                    {'value': '中', 'label': '中', 'tapd_value': '中'},
                    {'value': '低', 'label': '低', 'tapd_value': '低'}
                ],
                'data_source': 'error_fallback',
                'updated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'error': str(e)
            }), 200
    
    # 健康检查接口
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """健康检查"""
        return jsonify({
            "status": "healthy",
            "service": "OCR Monitor",
            "version": "2.0"
        }), 200
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({"error": "接口不存在"}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({"error": "服务器内部错误"}), 500 