// OCR校验任务监控看板 - 单页应用JavaScript

// 全局变量
let allTasks = [];
let filteredTasks = [];
let currentTaskId = null;
let refreshInterval;
let selectedTasks = new Set();

// 人员角色映射配置
const personToRoleMapping = {
    "刘青昀": "准确性环节人员",
    "郑凯鑫": "完备性环节人员", 
    "陈嫚婷": "OCR校验环节人员"
};

// 根据用户姓名获取对应的角色名称
function getUserRoleName(personName) {
    return personToRoleMapping[personName] || personName;
}

// 动态状态和优先级配置 - 从TAPD API获取
let tapdFieldConfig = {
    status_options: [
        {value: 'open', label: '未开始', tapd_value: 'open'},
        {value: 'progressing', label: '进行中', tapd_value: 'progressing'},
        {value: 'done', label: '已完成', tapd_value: 'done'}
    ],
    priority_options: [
        {value: '高', label: '高', tapd_value: '高'},
        {value: '中', label: '中', tapd_value: '中'},
        {value: '低', label: '低', tapd_value: '低'}
    ]
};

// 动态生成的映射对象
let statusMapping = {};
let statusClasses = {};
let priorityMapping = {};

// 初始化映射对象
function initializeFieldMappings() {
    // 初始化状态映射
    statusMapping = {};
    statusClasses = {};
    
    tapdFieldConfig.status_options.forEach(option => {
        const frontendValue = mapTapdToFrontendStatus(option.tapd_value);
        
        // 多种映射方式
        statusMapping[option.tapd_value] = option.label;
        statusMapping[option.value] = option.label;
        statusMapping[option.label] = option.label;
        statusMapping[frontendValue] = option.label;
        
        // 样式类映射
        statusClasses[option.label] = `status-${frontendValue}`;
    });
    
    // 添加额外的状态映射以确保兼容性
    statusMapping['completed'] = '已完成';
    statusMapping['processing'] = '进行中';
    statusMapping['pending'] = '未开始';
    statusMapping['done'] = '已完成';
    statusMapping['progressing'] = '进行中';
    statusMapping['open'] = '未开始';
    
    // 添加空状态的映射
    statusMapping[''] = '未开始';
    statusMapping[null] = '未开始';
    statusMapping[undefined] = '未开始';
    
    // 初始化优先级映射
    priorityMapping = {};
    tapdFieldConfig.priority_options.forEach(option => {
        priorityMapping[option.value] = option.label;
        priorityMapping[option.label] = option.label;
        priorityMapping[option.tapd_value] = option.label;
    });
    
    console.log('字段映射已初始化:', {
        statusMapping,
        statusClasses,
        priorityMapping
    });
}

// 将TAPD状态值映射为前端内部状态
function mapTapdToFrontendStatus(tapdStatus) {
    const mapping = {
        'open': '未开始',
        'progressing': '进行中', 
        'done': '已完成'
    };
    return mapping[tapdStatus] || '未开始';
}

// 将前端状态映射为TAPD状态值  
function mapFrontendToTapdStatus(frontendStatus) {
    const mapping = {
        '未开始': 'open',
        '进行中': 'progressing',
        '已完成': 'done'
    };
    return mapping[frontendStatus] || 'open';
}

// 初始化默认映射
initializeFieldMappings();

// 从TAPD API加载字段配置
async function loadTapdFieldConfig() {
    try {
        console.log('正在从TAPD API加载字段配置...');
        
        const response = await fetch('/api/tapd/task_fields');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const configData = await response.json();
        
        if (configData.success) {
            // 更新全局配置
            tapdFieldConfig.status_options = configData.status_options || tapdFieldConfig.status_options;
            tapdFieldConfig.priority_options = configData.priority_options || tapdFieldConfig.priority_options;
            
            // 重新初始化映射
            initializeFieldMappings();
            
            console.log('TAPD字段配置加载成功:', {
                data_source: configData.data_source,
                status_count: tapdFieldConfig.status_options.length,
                priority_count: tapdFieldConfig.priority_options.length
            });
            
            // 更新状态按钮
            updateStatusButtons();
            
        } else {
            console.warn('TAPD字段配置加载失败，使用默认配置');
        }
        
    } catch (error) {
        console.error('加载TAPD字段配置失败:', error);
        console.warn('将使用默认字段配置');
    }
}

// 更新状态按钮显示
function updateStatusButtons() {
    // 查找任务详情模态框中的状态按钮区域
    const statusButtonsContainer = document.querySelector('#taskDetailModal .status-buttons');
    if (statusButtonsContainer) {
        let buttonsHtml = '';
        
        tapdFieldConfig.status_options.forEach(option => {
            const frontendStatus = mapTapdToFrontendStatus(option.tapd_value);
            buttonsHtml += `
                <button class="btn btn-sm btn-outline-primary" 
                        onclick="updateTaskStatus(currentTaskId, '${option.label}')">
                    ${option.label}
                </button>
            `;
        });
        
        statusButtonsContainer.innerHTML = buttonsHtml;
        console.log('状态按钮已更新');
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化字段映射
    initializeFieldMappings();

    // 先加载TAPD字段配置
    loadTapdFieldConfig().then(() => {
        return loadAllData();
    }).then(() => {
        // 加载问题类型选项
        return loadAvailableProblemTypes();
    }).then(() => {
        // 启动定时刷新
        startAutoRefresh();
    }).catch(error => {
        console.error('页面初始化过程中发生错误:', error);
        showUpdateStatus('页面初始化失败，请刷新页面重试', 'danger');
    });

    // 设置任务筛选
    setupTaskFilters();

    // 设置个人页面逻辑
    setupPersonalPageLogic();
});

// 更新当前时间
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('current-time').textContent = timeString;
}

// 处理个人页面逻辑
function handlePersonalPage() {
    const config = window.personalPageConfig;
    
    console.log('处理个人页面逻辑，配置:', config);
    
    // 检查配置是否有效
    if (!config || !config.personName) {
        console.error('个人页面配置无效:', config);
        return;
    }
    
    // 修改页面标题和导航显示
    const navbarBrand = document.querySelector('.navbar-brand');
    if (navbarBrand) {
        navbarBrand.innerHTML = `
            <i class="bi bi-person-badge text-primary"></i>
            ${config.personName} - 个人任务看板
        `;
        console.log('已更新导航栏标题:', config.personName);
    } else {
        console.error('未找到 .navbar-brand 元素');
    }
}

// 加载所有数据
async function loadAllData() {
    try {
        showLoading('正在加载数据...');
        
        // 并行获取数据
        const [overviewResponse, personsResponse] = await Promise.all([
            fetch('/api/process_overview'),
            fetch('/api/config')
        ]);
        
        if (!overviewResponse.ok || !personsResponse.ok) {
            throw new Error('API请求失败');
        }
        
        const overviewData = await overviewResponse.json();
        const configData = await personsResponse.json();
        
        // 获取负责人列表（去重）
        const responsiblePersons = configData.ocr_validation?.responsible_persons || {};
        const uniquePersons = [...new Set(Object.values(responsiblePersons))];
        
        console.log('负责人列表:', uniquePersons);
        
        // 加载所有负责人的任务
        const taskPromises = uniquePersons.map(person => {
            console.log(`正在加载 ${person} 的任务...`);
            return fetch(`/api/person_tasks/${encodeURIComponent(person)}`);
        });
        
        const taskResponses = await Promise.all(taskPromises);
        let allPersonTasks = [];
        
        for (let i = 0; i < taskResponses.length; i++) {
            const person = uniquePersons[i];
            if (taskResponses[i].ok) {
                const taskData = await taskResponses[i].json();
                console.log(`${person} 的任务数据:`, taskData);
                if (taskData.tasks && Array.isArray(taskData.tasks)) {
                    allPersonTasks = allPersonTasks.concat(taskData.tasks);
                    console.log(`添加了 ${taskData.tasks.length} 个任务，总任务数: ${allPersonTasks.length}`);
                } else if (taskData.success && taskData.tasks && Array.isArray(taskData.tasks)) {
                    // 兼容不同的API返回格式
                    allPersonTasks = allPersonTasks.concat(taskData.tasks);
                    console.log(`添加了 ${taskData.tasks.length} 个任务（success格式），总任务数: ${allPersonTasks.length}`);
                } else {
                    console.warn(`${person} 的任务数据格式异常:`, taskData);
                }
            } else {
                console.error(`获取 ${person} 的任务失败:`, taskResponses[i].status);
            }
        }
        
        console.log('合并后的所有任务:', allPersonTasks.length);
        
        // 更新任务数据
        allTasks = processTaskData(allPersonTasks);
        filteredTasks = [...allTasks];
        
        console.log('处理后的任务数据:', allTasks.length);
        
        // 更新页面显示
        updateProcessPosition(responsiblePersons);
        updateTaskList();
        updateLastUpdateTime();
        
        // 如果是个人页面，应用筛选显示当前用户任务
        if (window.personalPageConfig && window.personalPageConfig.isPersonalPage) {
            console.log('个人页面模式 - 开始筛选任务...');
            console.log('当前用户:', window.personalPageConfig.personName);
            console.log('筛选前总任务数:', allTasks.length);
            
            // 显示所有任务的owner信息
            allTasks.forEach(task => {
                console.log(`任务${task.id}: 负责人="${task.owner}" (原始="${task.rawOwner}")`);
            });
            
            filterTasks(); // 应用筛选显示当前用户任务
            
            console.log('筛选后任务数:', filteredTasks.length);
        }
        
        showUpdateStatus('数据更新完成', 'success');
        
    } catch (error) {
        console.error('数据加载失败:', error);
        showUpdateStatus('数据加载失败，请检查网络连接', 'danger');
    }
}

// 处理任务数据，统一状态格式并过滤无效数据
function processTaskData(tasks) {
    console.log('开始处理任务数据，原始任务数量:', tasks.length);
    
    const processedTasks = tasks.filter(task => {
        // 首先过滤掉无效任务
        const taskId = task.id || task.Id || task.task_id || '';
        const taskName = task.name || task.Name || task.title || '';
        
        // ID和名称都不能为空
        if (!taskId || !taskName || taskId === '' || taskName === '') {
            console.log(`过滤无效任务: ID="${taskId}", Name="${taskName}"`);
            return false;
        }
        
        return true;
    }).map(task => {
        // 统一状态格式
        const originalStatus = task.status || task.Status || '';
        const simplifiedStatus = statusMapping[originalStatus] || '未开始';
        
        // 清理负责人名称，去掉分号和其他特殊符号
        const rawOwner = task.owner || task.Owner || task.responsible || '未分配';
        const cleanOwner = rawOwner.replace(/[;；,，\s]+$/, '').trim(); // 去掉末尾的分号、逗号、空格
        
        console.log(`任务${task.id}: 原始状态="${originalStatus}" -> 简化状态="${simplifiedStatus}", 原始负责人="${rawOwner}" -> 清理后="${cleanOwner}"`);
        
        return {
            ...task,
            simplifiedStatus: simplifiedStatus,
            originalStatus: originalStatus,
            id: task.id || task.Id || task.task_id,
            name: task.name || task.Name || task.title || '未命名任务',
            owner: cleanOwner,
            rawOwner: rawOwner, // 保存原始的owner信息用于调试
            priority: task.priority || task.Priority || 'medium',
            created: task.created || task.Created || '',
            description: task.description || task.Description || ''
        };
    });
    
    console.log('任务数据处理完成，处理后任务数量:', processedTasks.length);
    return processedTasks;
}

// 更新流程位置区域
function updateProcessPosition(responsiblePersons) {
    // 获取当前用户信息
    const currentPersonName = window.personalPageConfig && window.personalPageConfig.isPersonalPage 
        ? window.personalPageConfig.personName 
        : null;
    
    // 更新当前用户名称显示
    const userNameElement = document.getElementById('current-user-name');
    if (userNameElement) {
        userNameElement.textContent = currentPersonName || '访客用户';
    }
    
    // 生成流程图
    generateProcessFlow(currentPersonName, responsiblePersons);
}

// 生成流程图
function generateProcessFlow(currentUser, responsiblePersons) {
    const container = document.getElementById('process-flow');
    if (!container) return;
    
    // 按照mermaid逻辑正确的分列结构
    const flowColumns = [
        // 第1列：告警创建
        [
            {
                id: 'alert_creation',
                title: '告警创建',
                icon: 'bi-bell',
                type: 'auto',
                description: '通过TAPD API创建需求和任务'
            }
        ],
        // 第2列：完整性问题处理 和 准确性问题处理（两个并列分支）
        [
            {
                id: 'completeness_handling',
                title: '完整性问题处理',
                icon: 'bi-file-plus',
                responsible: '司马维维',
                type: 'manual',
                description: '补充缺失影像件'
            },
            {
                id: 'accuracy_handling',
                title: '准确性问题处理',
                icon: 'bi-search',
                responsible: '李玲',
                type: 'manual',
                description: '分析准确性问题的具体类型'
            }
        ],
        // 第3列：来自准确性问题处理的三个分支
        [
            {
                id: 'image_quality_handling',
                title: '影像件模糊处理',
                icon: 'bi-sliders',
                responsible: '李玲',
                type: 'manual',
                description: '调整OCR识别阈值'
            },
            {
                id: 'content_error_handling',
                title: '内容错误处理',
                icon: 'bi-exclamation-triangle',
                responsible: '李玲',
                type: 'manual',
                description: '与合作机构沟通核对'
            },
            {
                id: 'system_bug_handling',
                title: '系统问题修复',
                icon: 'bi-bug',
                responsible: '李玲',
                type: 'manual',
                description: '修复系统Bug或优化算法'
            }
        ],
        // 第4列：重新OCR和闭环验证
        [
            {
                id: 'ocr_rerun',
                title: '重新OCR识别',
                icon: 'bi-arrow-repeat',
                responsible: '陈俊',
                type: 'manual',
                description: '影像平台人员发起OCR识别'
            },
            {
                id: 'validation_check',
                title: '闭环验证',
                icon: 'bi-check-circle',
                type: 'auto',
                description: '重新检测数据状况'
            }
        ]
    ];
    
        let flowHTML = '<div class="process-flow-container d-flex align-items-center" style="min-height: 200px; padding: 20px; gap: 8px; overflow-x: auto;">';
    
    // 第1列：告警创建
    flowHTML += '<div class="flow-column d-flex flex-column align-items-center justify-content-center">';
    const alertStep = { id: 'alert_creation', title: '告警创建', icon: 'bi-bell', type: 'auto', description: '系统自动' };
    flowHTML += createStepElement(alertStep, currentUser);
    flowHTML += '</div>';
    
    // 箭头
    flowHTML += '<i class="bi-arrow-right" style="font-size: 1.1rem; color: #0d6efd; flex-shrink: 0; margin: 0 2px;"></i>';
    
    // 第2列：完整性问题处理（上）| 准确性问题处理（下）
    flowHTML += '<div class="flow-column d-flex flex-column align-items-center" style="gap: 12px;">';
    const completenessStep = { id: 'completeness_handling', title: '完整性问题处理', icon: 'bi-file-plus', responsible: '司马维维', type: 'manual', description: '补充缺失影像件' };
    flowHTML += createStepElement(completenessStep, currentUser);
    
    const accuracyStep = { id: 'accuracy_handling', title: '准确性问题处理', icon: 'bi-search', responsible: '李玲', type: 'manual', description: '分析准确性问题类型' };
    flowHTML += createStepElement(accuracyStep, currentUser);
    flowHTML += '</div>';
    
    // 箭头
    flowHTML += '<i class="bi-arrow-right" style="font-size: 1.1rem; color: #0d6efd; flex-shrink: 0; margin: 0 2px;"></i>';
    
    // 第3列：隐藏空白（上，对应完整性）| 三个子处理（下，对应准确性，框起来）
    flowHTML += '<div class="flow-column d-flex flex-column align-items-center" style="gap: 12px;">';
    
    // 隐藏的空白位置（对应完整性问题处理）
    flowHTML += '<div style="min-width: 140px; height: 80px; visibility: hidden;"></div>';
    
    // 三个子处理（对应准确性问题处理，用浅色边框框起来）
    flowHTML += '<div class="accuracy-sub-group d-flex" style="gap: 6px; border: 2px solid #e3f2fd; border-radius: 8px; padding: 6px; background: rgba(33, 150, 243, 0.05);">';
    const subSteps = [
        { id: 'image_quality_handling', title: '影像件模糊处理', icon: 'bi-sliders', responsible: '李玲', type: 'manual', description: '调整OCR识别阈值' },
        { id: 'content_error_handling', title: '内容错误处理', icon: 'bi-exclamation-triangle', responsible: '李玲', type: 'manual', description: '处理识别内容错误' },
        { id: 'system_bug_handling', title: '系统问题修复', icon: 'bi-bug', responsible: '李玲', type: 'manual', description: '修复系统Bug' }
    ];
    
    subSteps.forEach((step, index) => {
        flowHTML += createStepElement(step, currentUser, 'small-step');
    });
    
    flowHTML += '</div>'; // 结束accuracy-sub-group
    flowHTML += '</div>'; // 结束flow-column
    
    // 箭头
    flowHTML += '<i class="bi-arrow-right" style="font-size: 1.1rem; color: #0d6efd; flex-shrink: 0; margin: 0 2px;"></i>';
    
    // 第4列：重新OCR
    flowHTML += '<div class="flow-column d-flex flex-column align-items-center justify-content-center">';
    const ocrStep = { id: 'ocr_rerun', title: '重新OCR', icon: 'bi-arrow-repeat', responsible: '陈俊', type: 'manual', description: '重新OCR识别' };
    flowHTML += createStepElement(ocrStep, currentUser);
    flowHTML += '</div>';
    
    // 箭头
    flowHTML += '<i class="bi-arrow-right" style="font-size: 1.1rem; color: #0d6efd; flex-shrink: 0; margin: 0 2px;"></i>';
    
    // 第5列：闭环验证
    flowHTML += '<div class="flow-column d-flex flex-column align-items-center justify-content-center">';
    const validationStep = { id: 'validation_check', title: '闭环验证', icon: 'bi-check-circle', type: 'auto', description: '重新检测数据状况' };
    flowHTML += createStepElement(validationStep, currentUser);
    flowHTML += '</div>';
    
    flowHTML += '</div>';
    
    // 创建步骤元素的辅助函数
    function createStepElement(step, currentUser, className = '') {
        const isMyStep = step.responsible === currentUser;
        const stepStatus = getStepStatus(step, currentUser);
        
        // 获取该负责人的任务统计
        let taskCountText = '';
        if (step.responsible) {
            const personTasks = allTasks.filter(task => task.owner === step.responsible);
            const completedCount = personTasks.filter(task => task.simplifiedStatus === '已完成').length;
            const totalCount = personTasks.length;
            taskCountText = totalCount > 0 ? ` (${completedCount}/${totalCount})` : '';
        }
        
        const sizeStyle = className === 'small-step' ? 'min-width: 120px; max-width: 120px; font-size: 0.85rem;' : 'min-width: 140px; max-width: 160px;';
        
        return `
            <div class="process-step ${className} ${isMyStep ? 'my-step' : ''} ${stepStatus}" 
                 onclick="showStepDetail('${step.id}')"
                 data-bs-toggle="tooltip" 
                 data-bs-placement="top" 
                 title="负责人: ${step.responsible || '系统自动'}"
                 style="${sizeStyle}">
                <i class="step-icon ${step.icon}"></i>
                <div class="step-title">${step.title}</div>
                <div class="step-status" style="font-size: 0.7rem;">${step.description}</div>
                ${taskCountText ? `<small>${taskCountText}</small>` : ''}
            </div>
        `;
    }
    
    // 添加图例说明
    if (currentUser) {
        flowHTML += `
            <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px; text-align: center;">
                <small style="color: rgba(255,255,255,0.8);">
                    <span style="display: inline-block; width: 16px; height: 16px; background: rgba(255, 193, 7, 0.4); border: 2px solid #ffc107; border-radius: 4px; margin-right: 5px; position: relative;">
                        <span style="position: absolute; top: -6px; right: -6px; width: 12px; height: 12px; background: #ffc107; color: #212529; border-radius: 50%; font-size: 6px; display: flex; align-items: center; justify-content: center;">⭐</span>
                    </span>
                    表示您负责的流程步骤
                </small>
            </div>
        `;
    }
    
    container.innerHTML = flowHTML;
    
    // 初始化Bootstrap tooltip
    const tooltipTriggerList = container.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl, {
        html: true,
        delay: { show: 500, hide: 100 }
    }));
}



// 获取步骤状态
function getStepStatus(step, currentUser) {
    if (!step.responsible || step.type === 'auto') {
        return 'completed'; // 自动步骤默认完成
    }
    
    // 基于任务数据判断状态
    const userTasks = allTasks.filter(task => task.owner === step.responsible);
    const pendingTasks = userTasks.filter(task => task.simplifiedStatus === '未开始');
    const inProgressTasks = userTasks.filter(task => task.simplifiedStatus === '进行中');
    
    if (inProgressTasks.length > 0) {
        return 'current';
    } else if (pendingTasks.length > 0) {
        return 'pending';
    } else {
        return 'completed';
    }
}

// 显示步骤详情
function showStepDetail(stepId) {
    const stepDetails = {
        'data_collection': {
            title: '数据采集',
            description: '系统自动从数据库提取OCR三性校验明细表',
            details: [
                '• 触发条件：每轮OCR识别任务结束后',
                '• 数据来源：OCR三性校验明细表',
                '• 处理方式：后台跑批任务自动执行',
                '• 用户影响：无直接操作，为后续流程提供数据基础'
            ],
            responsible: '系统自动',
            status: '自动触发'
        },
        'data_analysis': {
            title: '数据统计分析',
            description: '检测OCR校验结果，生成监控报告',
            details: [
                '• 检测是否存在"未通过"借据',
                '• 按问题类型（完备性/准确性）分类统计',
                '• 生成OCR三性校验数据监控报告',
                '• 若无问题则流程结束，有问题则继续后续流程'
            ],
            responsible: '系统自动',
            status: '数据分析中'
        },
        'alert_creation': {
            title: '告警创建',
            description: '通过TAPD API创建需求和任务',
            details: [
                '• 创建TAPD需求：【OCR三性校验告警】项目检出异常数据',
                '• 需求详情包含监控报告内容',
                '• 自动创建子任务并关联责任人',
                '• 通过企业微信发送通知提醒'
            ],
            responsible: '系统自动',
            status: '告警生成'
        },

        'completeness_handling': {
            title: '完备性问题处理',
            description: '处理影像件缺失或不完整的问题',
            details: [
                '• 整理缺失影像件清单',
                '• 与合作机构沟通补传时间',
                '• 跟进影像件补传进度',
                '• 确认补传完成后更新任务状态'
            ],
            responsible: '司马维维（产品人员）',
            status: '处理中'
        },
        'accuracy_handling': {
            title: '准确性问题处理',
            description: '分析准确性问题的具体类型和原因',
            details: [
                '• 分析OCR识别失败的具体原因',
                '• 判断是影像件质量问题还是内容错误',
                '• 确定是否存在系统技术问题',
                '• 将问题分类后分配给对应的处理人员'
            ],
            responsible: '李玲（开发人员）',
            status: '待分析'
        },
        'image_quality_handling': {
            title: '影像件模糊处理',
            description: '调整OCR识别阈值解决影像质量问题',
            details: [
                '• 分析影像件清晰度和质量',
                '• 调整OCR识别的敏感度阈值',
                '• 优化图像预处理算法',
                '• 测试阈值调整效果'
            ],
            responsible: '李玲（开发人员）',
            status: '处理中'
        },
        'content_error_handling': {
            title: '内容错误处理',
            description: '处理OCR识别内容错误的问题',
            details: [
                '• 分析OCR识别结果中的内容错误',
                '• 检查原始影像件内容是否清晰完整',
                '• 优化OCR识别算法或规则配置',
                '• 修正识别模板和字典库配置'
            ],
            responsible: '李玲（开发人员）',
            status: '处理中'
        },
        'system_bug_handling': {
            title: '系统问题修复',
            description: '修复OCR系统Bug或优化算法',
            details: [
                '• 定位系统技术问题根因',
                '• 修复OCR识别算法Bug',
                '• 优化系统性能和准确性',
                '• 进行系统测试验证'
            ],
            responsible: '李玲（开发人员）',
            status: '修复中'
        },
        'ocr_rerun': {
            title: '重新OCR识别',
            description: '影像平台人员发起新的OCR识别任务',
            details: [
                '• 接收到重新识别任务通知',
                '• 确认影像件或系统问题已解决',
                '• 启动新的OCR识别批次',
                '• OCR任务完成后自动触发重新跑批检测'
            ],
            responsible: '陈俊（影像平台人员）',
            status: '待执行'
        },
        'validation_check': {
            title: '闭环验证',
            description: '重新检测数据状况，确保问题已解决',
            details: [
                '• OCR识别完成后自动触发数据检测',
                '• 若仍存在问题：重新创建告警需求',
                '• 若问题已解决：流程正常结束',
                '• 记录整个处理过程的时间和效果'
            ],
            responsible: '系统自动',
            status: '验证中'
        }
    };
    
    const stepInfo = stepDetails[stepId];
    if (!stepInfo) {
        console.log('未找到步骤详情:', stepId);
        return;
    }
    
    // 创建详情显示的HTML
    const detailHTML = `
        <div class="step-detail-popup" style="
            position: fixed; top: 50%; left: 50%; 
            transform: translate(-50%, -50%);
            background: white; 
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        ">
            <div style="background: linear-gradient(135deg, #2c3e50, #3498db); color: white; padding: 20px; border-radius: 15px 15px 0 0;">
                <h5 style="margin: 0; display: flex; align-items: center; justify-content: space-between;">
                    <span><i class="bi bi-info-circle me-2"></i>${stepInfo.title}</span>
                    <button onclick="closeStepDetail()" style="
                        background: none; border: none; color: white; 
                        font-size: 1.5rem; cursor: pointer;
                    ">&times;</button>
                </h5>
                <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 0.9rem;">${stepInfo.description}</p>
            </div>
            <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <strong style="color: #2c3e50;"><i class="bi bi-person me-1"></i>负责人：</strong>
                    <span style="color: #0d6efd;">${stepInfo.responsible}</span>
                </div>
                <div style="margin-bottom: 15px;">
                    <strong style="color: #2c3e50;"><i class="bi bi-flag me-1"></i>当前状态：</strong>
                    <span class="badge bg-info">${stepInfo.status}</span>
                </div>
                <div>
                    <strong style="color: #2c3e50; display: block; margin-bottom: 10px;">
                        <i class="bi bi-list-ul me-1"></i>详细流程：
                    </strong>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #0d6efd;">
                        ${stepInfo.details.map(detail => `<div style="margin-bottom: 8px; color: #495057;">${detail}</div>`).join('')}
                    </div>
                </div>
            </div>
        </div>
        <div class="step-detail-overlay" onclick="closeStepDetail()" style="
            position: fixed; top: 0; left: 0; 
            width: 100%; height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        "></div>
    `;
    
    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', detailHTML);
}

// 关闭步骤详情
function closeStepDetail() {
    const popup = document.querySelector('.step-detail-popup');
    const overlay = document.querySelector('.step-detail-overlay');
    if (popup) popup.remove();
    if (overlay) overlay.remove();
}



// 更新任务列表
function updateTaskList() {
    const container = document.getElementById('task-list');
    const countBadge = document.getElementById('task-count');
    
    if (!container) return;
    
    countBadge.textContent = filteredTasks.length;
    
    if (filteredTasks.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-inbox display-1"></i>
                <p class="mt-3">没有找到匹配的任务</p>
                <button class="btn btn-outline-primary" onclick="clearFilters()">清除筛选条件</button>
            </div>
        `;
        return;
    }
    
    container.innerHTML = '';
    
    filteredTasks.forEach(task => {
        const taskElement = createTaskElement(task);
        container.appendChild(taskElement);
    });
    
    updateBatchButtons();
}

// 创建任务元素
function createTaskElement(task) {
    const taskDiv = document.createElement('div');
    taskDiv.className = `task-item ${statusClasses[task.simplifiedStatus] || ''}`;
    taskDiv.dataset.taskId = task.id;
    
    const priorityBadge = getPriorityBadge(task.priority);
    const statusBadge = getStatusBadge(task.simplifiedStatus);
    
    taskDiv.innerHTML = `
        <div class="d-flex align-items-start">
            <div class="form-check me-3">
                <input class="form-check-input" type="checkbox" 
                       onchange="toggleTaskSelection('${task.id}')" 
                       id="task-${task.id}">
            </div>
            <div class="flex-grow-1">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="task-meta">
                        <span style="color: #6c757d;"><i class="bi bi-person"></i> ${task.owner}</span>
                        <span style="color: #6c757d;"><i class="bi bi-calendar"></i> ${formatDate(task.created)}</span>
                        <span>${priorityBadge}</span>
                        <span>${statusBadge}</span>
                    </div>
                    <button class="btn btn-sm btn-link text-primary p-0 ms-2" onclick="showTaskDetail('${task.id}')" style="text-decoration: none; font-weight: 500; white-space: nowrap;">
                        <i class="bi bi-eye me-1"></i>查看详情
                    </button>
                </div>
                <h6 class="mb-2" style="color: #2c3e50; font-weight: 600;">${task.name}</h6>
                <p class="mb-0" style="color: #495057;">${task.description || '暂无描述'}</p>
            </div>
        </div>
    `;
    
    return taskDiv;
}

// 获取优先级徽章
function getPriorityBadge(priority) {
    // 使用动态优先级映射
    const displayLabel = priorityMapping[priority] || priority || '中';
    
    // 根据优先级标签确定亮色背景和深色文字
    const colorMap = {
        '紧急': { bg: '#ffebeb', text: '#dc3545' },
        '高': { bg: '#ffebeb', text: '#dc3545' }, 
        '中': { bg: '#fff4e6', text: '#f57c00' },
        '低': { bg: '#e8f5e8', text: '#198754' }
    };
    
    const colors = colorMap[displayLabel] || { bg: '#e8f5e8', text: '#198754' };
    
    return `<span class="badge" style="background-color: ${colors.bg}; color: ${colors.text}; font-weight: 600; border: 1px solid ${colors.text}20;">${displayLabel}</span>`;
}

// 获取状态徽章
function getStatusBadge(status) {
    const statusMap = {
        '未开始': '<span class="badge" style="background-color: #fff3cd; color: #856404; font-weight: 600; border: 1px solid #ffc10730;">未开始</span>',
        '进行中': '<span class="badge" style="background-color: #e7f3ff; color: #0d6efd; font-weight: 600; border: 1px solid #0d6efd30;">进行中</span>',
        '已完成': '<span class="badge" style="background-color: #eaf7ed; color: #198754; font-weight: 600; border: 1px solid #19875430;">已完成</span>'
    };
    return statusMap[status] || '<span class="badge" style="background-color: #fff3cd; color: #856404; font-weight: 600; border: 1px solid #ffc10730;">未知</span>';
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '未知';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    } catch {
        return dateString;
    }
}

// 任务筛选
function filterTasks() {
    const statusFilter = document.getElementById('status-filter').value;
    const searchText = document.getElementById('search-input').value.toLowerCase();
    
            filteredTasks = allTasks.filter(task => {
        // 如果是个人页面，只显示当前用户的任务
        if (window.personalPageConfig && window.personalPageConfig.isPersonalPage) {
            // 清理任务负责人姓名，去掉分号等特殊字符
            const cleanTaskOwner = (task.owner || '').replace(/[;；,，\s]+$/, '').trim();
            
            // 获取当前用户对应的角色名称
            const userRoleName = getUserRoleName(window.personalPageConfig.personName);
            
            if (cleanTaskOwner !== userRoleName) {
                console.log(`任务${task.id}被个人筛选过滤：任务负责人="${task.owner}" (清理后="${cleanTaskOwner}") vs 用户角色="${userRoleName}" (用户="${window.personalPageConfig.personName}")`);
                return false;
            }
        }
        
        // 状态筛选
        if (statusFilter && task.simplifiedStatus !== statusFilter) {
            console.log(`任务${task.id}被状态筛选过滤：任务状态="${task.simplifiedStatus}" vs 筛选条件="${statusFilter}"`);
            return false;
        }
        
        // 文本搜索
        if (searchText) {
            const searchableText = `${task.name} ${task.description} ${task.owner}`.toLowerCase();
            if (!searchableText.includes(searchText)) {
                return false;
            }
        }

        return true;
    });

    // 统计各状态的任务数量
    const statusCounts = {
        '未开始': 0,
        '进行中': 0,
        '已完成': 0
    };

    filteredTasks.forEach(task => {
        if (statusCounts.hasOwnProperty(task.simplifiedStatus)) {
            statusCounts[task.simplifiedStatus]++;
        }
    });
    
    updateTaskList();
}



// 清除筛选条件
function clearFilters() {
    document.getElementById('status-filter').value = '';
    document.getElementById('search-input').value = '';
    filterTasks();
}



// 切换任务选择
function toggleTaskSelection(taskId) {
    if (selectedTasks.has(taskId)) {
        selectedTasks.delete(taskId);
    } else {
        selectedTasks.add(taskId);
    }
    updateBatchButtons();
}

// 更新批量操作按钮状态
function updateBatchButtons() {
    const completeBtn = document.getElementById('batch-complete-btn');
    const tagBtn = document.getElementById('batch-tag-btn');
    
    const hasSelection = selectedTasks.size > 0;
    completeBtn.disabled = !hasSelection;
    tagBtn.disabled = !hasSelection;
    
    if (hasSelection) {
        completeBtn.textContent = `批量完成 (${selectedTasks.size})`;
        tagBtn.textContent = `批量标记问题 (${selectedTasks.size})`;
    } else {
        completeBtn.textContent = '批量完成';
        tagBtn.textContent = '批量标记问题';
    }
}

// 批量更新状态
async function batchUpdateStatus(newStatus) {
    if (selectedTasks.size === 0) return;
    
    try {
        showLoading(`正在批量设置为${newStatus}...`);
        
        const updatePromises = Array.from(selectedTasks).map(taskId => 
            updateTaskStatus(taskId, newStatus, false)
        );
        
        await Promise.all(updatePromises);
        
        // 清除选择
        selectedTasks.clear();
        document.querySelectorAll('.task-item input[type="checkbox"]').forEach(cb => {
            cb.checked = false;
        });
        
        // 重新加载数据
        await loadAllData();
        
        showUpdateStatus(`成功批量设置 ${selectedTasks.size} 个任务为${newStatus}`, 'success');
        
    } catch (error) {
        console.error('批量更新失败:', error);
        showUpdateStatus('批量更新失败，请重试', 'danger');
    }
}

// 更新单个任务状态
async function updateTaskStatus(taskId, newStatus, reload = true) {
    try {
        if (reload) {
            showLoading(`正在设置任务状态为${newStatus}...`);
        }
        
        // 准备请求数据，包含操作审计信息
        const requestData = { status: newStatus };
        
        // 如果是个人页面，添加操作者信息用于审计
        if (window.personalPageConfig && window.personalPageConfig.isPersonalPage) {
            requestData.person_code = window.personalPageConfig.personCode;
            requestData.person_name = window.personalPageConfig.personName;
        }
        
        const response = await fetch(`/api/task/${taskId}/update`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            throw new Error('更新请求失败');
        }
        
        const result = await response.json();
        
        if (reload) {
            // 重新加载数据
            await loadAllData();
            showUpdateStatus(`任务状态已更新为: ${newStatus}`, 'success');
            
            // 关闭任务详情modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('taskDetailModal'));
            if (modal) {
                modal.hide();
            }
        }
        
        return result;
        
    } catch (error) {
        console.error('更新任务状态失败:', error);
        if (reload) {
            showUpdateStatus('更新任务状态失败，请重试', 'danger');
        }
        throw error;
    }
}

// 显示任务详情
function showTaskDetail(taskId) {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) return;
    
    currentTaskId = taskId;
    
    const content = document.getElementById('task-detail-content');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6 style="color: #2c3e50; font-weight: 600;">基本信息</h6>
                <table class="table table-sm table-bordered">
                    <tr><th style="background-color: #f8f9fa; color: #2c3e50; font-weight: 600;">任务名称</th><td style="color: #495057;">${task.name}</td></tr>
                    <tr><th style="background-color: #f8f9fa; color: #2c3e50; font-weight: 600;">负责人</th><td style="color: #495057;">${task.owner}</td></tr>
                    <tr><th style="background-color: #f8f9fa; color: #2c3e50; font-weight: 600;">当前状态</th><td>${getStatusBadge(task.simplifiedStatus)}</td></tr>
                    <tr><th style="background-color: #f8f9fa; color: #2c3e50; font-weight: 600;">优先级</th><td>${getPriorityBadge(task.priority)}</td></tr>
                    <tr><th style="background-color: #f8f9fa; color: #2c3e50; font-weight: 600;">创建时间</th><td style="color: #495057;">${formatDate(task.created)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 style="color: #2c3e50; font-weight: 600;">任务描述</h6>
                <div class="p-3 bg-light rounded" style="border: 1px solid #dee2e6;">
                    <p style="color: #495057; margin: 0;">${task.description || '暂无描述'}</p>
                </div>
                
                <h6 style="color: #2c3e50; font-weight: 600; margin-top: 20px;">原始状态</h6>
                <div class="p-2 bg-light rounded" style="border: 1px solid #dee2e6;">
                    <code style="color: #e83e8c; background-color: transparent;">${task.originalStatus}</code>
                </div>
                
                ${(task.name.includes('准确性问题') || task.name.startsWith('准确性问题-')) ? `
                <h6 style="color: #2c3e50; font-weight: 600; margin-top: 20px;">TAPD标签</h6>
                <div class="p-2 bg-light rounded" style="border: 1px solid #dee2e6;" id="tapd-label-display">
                    <small class="text-muted">正在加载...</small>
                </div>` : ''}
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('taskDetailModal'));
    modal.show();
}

// 发送通知相关函数
function sendNotification(personName) {
    document.getElementById('recipient').value = personName;
    document.getElementById('message').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('notificationModal'));
    modal.show();
}

async function confirmSendNotification() {
    const recipient = document.getElementById('recipient').value;
    const message = document.getElementById('message').value;
    
    if (!message.trim()) {
        alert('请输入消息内容');
        return;
    }
    
    try {
        const response = await fetch('/api/send_notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                person_name: recipient,
                message: message
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showUpdateStatus(`通知已发送给 ${recipient}`, 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
            modal.hide();
        } else {
            showUpdateStatus('发送通知失败: ' + result.error, 'danger');
        }
        
    } catch (error) {
        console.error('发送通知失败:', error);
        showUpdateStatus('发送通知失败，请重试', 'danger');
    }
}

// 刷新所有数据
async function refreshAllData() {
    const btn = document.getElementById('refresh-btn');
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>刷新中...';
    
    try {
        // 先调用后台刷新API
        await fetch('/api/cache/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ category: 'all' })
        });
        
        // 等待一秒后重新加载数据
        setTimeout(async () => {
            await loadAllData();
            btn.disabled = false;
            btn.innerHTML = originalText;
        }, 1000);
        
    } catch (error) {
        console.error('刷新失败:', error);
        btn.disabled = false;
        btn.innerHTML = originalText;
        showUpdateStatus('刷新失败，请重试', 'danger');
    }
}

// 启动自动刷新
function startAutoRefresh() {
    // 设置定时刷新（每5分钟）
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    
    refreshInterval = setInterval(() => {
        console.log('执行定时刷新...');
        loadAllData();
    }, 300000); // 5分钟
    
    console.log('自动刷新已启动（每5分钟）');
}

// 显示加载状态
function showLoading(message) {
    const statusElement = document.getElementById('update-status');
    if (statusElement) {
        statusElement.innerHTML = `<span class="spinner-border spinner-border-sm me-2"></span>${message}`;
    }
}

// 显示更新状态
function showUpdateStatus(message, type = 'info') {
    const statusElement = document.getElementById('update-status');
    if (statusElement) {
        statusElement.className = `alert alert-${type}`;
        statusElement.innerHTML = `<i class="bi bi-info-circle"></i> ${message}`;
        
        // 3秒后恢复默认状态
        setTimeout(() => {
            statusElement.className = 'alert alert-info';
            statusElement.innerHTML = '<i class="bi bi-info-circle"></i> 数据每5分钟自动更新一次';
        }, 3000);
    }
}

// 更新最后更新时间
function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN');
    const element = document.getElementById('last-update-time');
    if (element) {
        element.textContent = timeString;
    }
}

// 复制个性化链接到剪贴板
async function copyPersonalLink(link) {
    try {
        await navigator.clipboard.writeText(link);
        showUpdateStatus('专属链接已复制到剪贴板！', 'success');
    } catch (err) {
        // 降级处理：使用传统方法
        try {
            const textArea = document.createElement('textarea');
            textArea.value = link;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showUpdateStatus('专属链接已复制到剪贴板！', 'success');
        } catch (fallbackErr) {
            console.error('复制失败:', fallbackErr);
            showUpdateStatus('复制失败，请手动选择链接复制', 'warning');
            // 选中链接文本以便用户手动复制
            selectTextInElement(link);
        }
    }
}

// 选中元素中的文本（辅助函数）
function selectTextInElement(text) {
    // 创建一个临时的文本区域来选中文本
    const tempDiv = document.createElement('div');
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.innerHTML = text;
    document.body.appendChild(tempDiv);
    
    if (window.getSelection) {
        const selection = window.getSelection();
        const range = document.createRange();
        range.selectNodeContents(tempDiv);
        selection.removeAllRanges();
        selection.addRange(range);
    }
    
    setTimeout(() => {
        document.body.removeChild(tempDiv);
    }, 100);
}

// 标签管理相关变量
let currentTaskTags = [];
let availableProblemTypes = [];  // 存储可用的问题类型

// 标签类型映射
const tagTypeMap = {
    'accuracy_image_blur': 'type-accuracy-defect',
    'accuracy_content_error': 'type-accuracy-error',
    'completeness_missing': 'type-completeness',
    'completeness_extra': 'type-completeness',
    'accuracy_page_error': 'type-accuracy-error',
    'accuracy_format_error': 'type-accuracy-error',
    'system_issue': 'type-system',
    'no_issue': 'type-no-problem'
};

// 加载可用的问题类型
async function loadAvailableProblemTypes() {
    try {
        const response = await fetch('/api/ocr_problem_types');
        const result = await response.json();
        
        if (result.success) {
            availableProblemTypes = result.problem_types || [];
            console.log('问题类型加载成功:', availableProblemTypes);
            
            // 更新标签选择器
            updateProblemTypeSelectors();
        } else {
            console.error('加载问题类型失败:', result.error);
        }
    } catch (error) {
        console.error('加载问题类型异常:', error);
    }
}

// 更新标签选择器
function updateProblemTypeSelectors() {
    const selectors = [
        document.getElementById('problem-type-select'),
        document.getElementById('batch-problem-type-select')
    ];
    
    // 只显示用户指定的三个准确性问题类型
    const allowedTypes = [
        { value: 'accuracy_image_blur', label: '准确性-影像件模糊' },
        { value: 'accuracy_content_error', label: '准确性-内容错误' },
        { value: 'system_issue', label: '准确性-系统问题' }
    ];
    
    selectors.forEach(selector => {
        if (selector) {
            // 清空现有选项，保留默认选项
            selector.innerHTML = '<option value="">选择问题类型</option>';
            
            // 只添加允许的三个选项
            allowedTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.value;
                option.textContent = type.label;
                selector.appendChild(option);
            });
        }
    });
}

// 添加问题标签
function addProblemTag() {
    // 检查是否为准确性问题任务
    const task = allTasks.find(t => t.id === currentTaskId);
    if (!task || !(task.name.includes('准确性问题') || task.name.startsWith('准确性问题-'))) {
        alert('只有准确性问题相关的任务才能添加标签');
        return;
    }
    
    const select = document.getElementById('problem-type-select');
    const selectedValue = select.value;
    
    if (!selectedValue) {
        alert('请选择问题类型');
        return;
    }
    
    // 检查是否已存在相同标签
    if (currentTaskTags.includes(selectedValue)) {
        alert('该标签已存在');
        return;
    }
    
    // 添加标签到数组
    currentTaskTags.push(selectedValue);
    
    // 更新显示
    updateTagsDisplay();
    
    // 重置选择
    select.value = '';
}

// 删除标签
function removeTag(tagText) {
    // 检查是否为准确性问题任务
    const task = allTasks.find(t => t.id === currentTaskId);
    if (!task || !(task.name.includes('准确性问题') || task.name.startsWith('准确性问题-'))) {
        alert('只有准确性问题相关的任务才能删除标签');
        return;
    }
    
    const index = currentTaskTags.indexOf(tagText);
    if (index > -1) {
        currentTaskTags.splice(index, 1);
        updateTagsDisplay();
    }
}

// 清除所有标签
function clearAllTags() {
    // 检查是否为准确性问题任务
    const task = allTasks.find(t => t.id === currentTaskId);
    if (!task || !(task.name.includes('准确性问题') || task.name.startsWith('准确性问题-'))) {
        alert('只有准确性问题相关的任务才能清除标签');
        return;
    }
    
    if (currentTaskTags.length === 0) {
        return;
    }
    
    if (confirm('确定要清除所有标签吗？')) {
        currentTaskTags = [];
        updateTagsDisplay();
    }
}

// 更新标签显示
function updateTagsDisplay() {
    const display = document.getElementById('problem-tags-display');
    
    if (currentTaskTags.length === 0) {
        display.innerHTML = '<small class="text-muted">暂无标签</small>';
        return;
    }
    
    let html = '';
    currentTaskTags.forEach(tag => {
        // 根据标签值查找对应的标签信息
        const tagInfo = availableProblemTypes.find(pt => pt.value === tag || pt.type === tag);
        const tagClass = tagTypeMap[tag] || '';
        const displayLabel = tagInfo ? tagInfo.label : tag;
        
        html += `
            <span class="problem-tag ${tagClass}">
                ${displayLabel}
                <span class="remove-tag" onclick="removeTag('${tag}')">&times;</span>
            </span>
        `;
    });
    
    display.innerHTML = html;
}

// 保存任务更改
async function saveTaskChanges() {
    if (!currentTaskId) {
        alert('没有选中的任务');
        return;
    }
    
    // 检查是否为准确性问题任务
    const task = allTasks.find(t => t.id === currentTaskId);
    if (!task || !(task.name.includes('准确性问题') || task.name.startsWith('准确性问题-'))) {
        alert('只有准确性问题相关的任务才能保存标签');
        return;
    }
    
    try {
        // 显示保存中状态
        showUpdateStatus('正在保存到TAPD...', 'info');
        
        // 准备要保存的数据
        const saveData = {
            task_id: currentTaskId,
            problem_tags: currentTaskTags
        };
        
        // 调用TAPD API保存标签数据
        const response = await fetch('/api/task/save_tags', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(saveData)
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            console.log('标签已保存到TAPD:', result);
            showUpdateStatus(`任务标签已保存到TAPD (${result.tags_count}个标签)`, 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('taskDetailModal'));
            if (modal) {
                modal.hide();
            }
        } else {
            const errorMsg = result.error || '保存失败';
            console.error('保存标签到TAPD失败:', errorMsg);
            showUpdateStatus(`保存失败: ${errorMsg}`, 'danger');
        }
        
    } catch (error) {
        console.error('保存标签失败:', error);
        showUpdateStatus('网络错误，保存失败', 'danger');
    }
}

// 加载任务标签
async function loadTaskTags(taskId) {
    // 初始化当前任务标签
    currentTaskTags = [];
    
    try {
        console.log(`正在从TAPD加载任务${taskId}的标签...`);
        
        // 从TAPD API加载标签
        const response = await fetch(`/api/task/${taskId}/tags`);
        let result = null;
        
        if (response.ok) {
            result = await response.json();
            if (result.success) {
                currentTaskTags = result.problem_tags || [];
                console.log(`从TAPD加载任务${taskId}的标签成功:`, currentTaskTags);
                
                if (result.label_string) {
                    console.log(`TAPD原始label字段值: "${result.label_string}"`);
                }
            } else {
                const errorMsg = result.error || '获取标签失败';
                console.error(`从TAPD获取任务${taskId}标签失败:`, errorMsg);
                showUpdateStatus(`获取任务标签失败: ${errorMsg}`, 'warning');
            }
        } else {
            console.error(`TAPD API响应错误: ${response.status}`);
            showUpdateStatus('获取任务标签失败，请稍后重试', 'warning');
        }
        
        // 更新标签显示
        setTimeout(() => {
            updateTagsDisplay();
            updateTapdLabelDisplay(result);
        }, 100);
        
    } catch (error) {
        console.error('加载任务标签失败:', error);
        currentTaskTags = [];
        showUpdateStatus('网络错误，无法获取任务标签', 'warning');
        
        setTimeout(() => {
            updateTagsDisplay();
            updateTapdLabelDisplay(null); // 错误情况下传入null
        }, 100);
    }
}

// 更新TAPD标签显示
function updateTapdLabelDisplay(tapdResult) {
    const display = document.getElementById('tapd-label-display');
    if (!display) return;
    
    if (tapdResult && tapdResult.success) {
        const labelString = tapdResult.label_string || '';
        const lastUpdated = tapdResult.last_updated || '';
        
        if (labelString) {
            display.innerHTML = `
                <div style="margin-bottom: 8px;">
                    <strong>原始标签:</strong> <code style="color: #0d6efd;">${labelString}</code>
                </div>
                ${lastUpdated ? `<small class="text-muted">最后更新: ${lastUpdated}</small>` : ''}
            `;
        } else {
            display.innerHTML = '<small class="text-muted">暂无TAPD标签</small>';
        }
    } else {
        display.innerHTML = '<small class="text-danger">标签加载失败</small>';
    }
}

// 修改原有的showTaskDetail函数，添加标签功能
const originalShowTaskDetail = showTaskDetail;
showTaskDetail = async function(taskId) {
    originalShowTaskDetail(taskId);
    
    // 只有任务名称包含"准确性问题"的任务才加载和显示标签功能
    const task = allTasks.find(t => t.id === taskId);
    if (task && (task.name.includes('准确性问题') || task.name.startsWith('准确性问题-'))) {
        await loadTaskTags(taskId);
        // 显示标签相关的模态框footer部分
        const tagsSection = document.querySelector('#taskDetailModal .modal-footer .row:nth-child(2)');
        if (tagsSection) {
            tagsSection.style.display = 'block';
        }
    } else {
        // 隐藏标签相关的模态框footer部分
        const tagsSection = document.querySelector('#taskDetailModal .modal-footer .row:nth-child(2)');
        if (tagsSection) {
            tagsSection.style.display = 'none';
        }
    }
};

// 显示批量标记问题模态框
function showBatchProblemTagModal() {
    if (selectedTasks.size === 0) {
        alert('请先选择要标记的任务');
        return;
    }
    
    // 更新选中任务数量
    document.getElementById('batch-tag-count').textContent = selectedTasks.size;
    
    // 显示选中的任务列表
    const selectedTasksList = document.getElementById('batch-selected-tasks');
    let taskListHtml = '';
    
    selectedTasks.forEach(taskId => {
        const task = allTasks.find(t => t.id === taskId);
        if (task) {
            taskListHtml += `
                <div class="mb-1">
                    <i class="bi bi-arrow-right me-1"></i>
                    <strong>${task.name}</strong> 
                    <small class="text-muted">(负责人: ${task.owner})</small>
                </div>
            `;
        }
    });
    
    selectedTasksList.innerHTML = taskListHtml || '<div class="text-muted">未找到选中的任务</div>';
    
    // 重置表单
    document.getElementById('batch-problem-type-select').value = '';
    document.getElementById('batch-replace-tags').checked = false;
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchProblemTagModal'));
    modal.show();
}

// 确认批量标记问题
async function confirmBatchProblemTag() {
    const problemType = document.getElementById('batch-problem-type-select').value;
    const replaceTags = document.getElementById('batch-replace-tags').checked;
    
    if (!problemType) {
        alert('请选择问题类型');
        return;
    }
    
    if (selectedTasks.size === 0) {
        alert('没有选中的任务');
        return;
    }
    
    try {
        showLoading(`正在为 ${selectedTasks.size} 个任务添加标签...`);
        
        // 准备批量标记的数据
        const batchData = {
            task_ids: Array.from(selectedTasks),
            problem_tag: problemType,
            replace_existing: replaceTags
        };
        
        // 调用批量标记API
        const response = await fetch('/api/task/batch_save_tags', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(batchData)
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            console.log('批量标记成功:', result);
            
            // 显示成功信息
            const successCount = result.success_count || selectedTasks.size;
            const failureCount = result.failure_count || 0;
            
            let message = `成功为 ${successCount} 个任务添加标签"${problemType}"`;
            if (failureCount > 0) {
                message += `, ${failureCount} 个任务标记失败`;
            }
            
            showUpdateStatus(message, 'success');
            
            // 如果有失败的任务，显示详细信息
            if (result.failed_tasks && result.failed_tasks.length > 0) {
                console.warn('标记失败的任务:', result.failed_tasks);
            }
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchProblemTagModal'));
            if (modal) {
                modal.hide();
            }
            
            // 清除选择
            selectedTasks.clear();
            document.querySelectorAll('.task-item input[type="checkbox"]').forEach(cb => {
                cb.checked = false;
            });
            updateBatchButtons();
            
            // 重新加载数据
            await loadAllData();
            
        } else {
            const errorMsg = result.error || '批量标记失败';
            console.error('批量标记失败:', errorMsg);
            showUpdateStatus(`批量标记失败: ${errorMsg}`, 'danger');
        }
        
    } catch (error) {
        console.error('批量标记问题标签失败:', error);
        showUpdateStatus('网络错误，批量标记失败', 'danger');
    }
} 