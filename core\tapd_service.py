#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TAPD业务服务 - Core层
提供高级的TAPD业务操作，基于Utils层的TAPDClient
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from utils import TAPDClient, ConfigLoader, format_status, format_priority_for_display


class TAPDService:
    """TAPD业务服务"""
    
    def __init__(self, config_loader: Optional[ConfigLoader] = None):
        """
        初始化TAPD业务服务
        
        Args:
            config_loader: 配置加载器实例
        """
        self.logger = logging.getLogger(__name__)
        self.config_loader = config_loader or ConfigLoader()
        self.tapd_client = TAPDClient(self.config_loader)
        
        # 获取配置
        self.workspace_id = self.config_loader.get_workspace_id()
        self.responsible_persons = self.config_loader.get_responsible_persons()
        
        self.logger.info("TAPD业务服务初始化完成")
    
    async def get_person_tasks(self, person_name: str) -> List[Dict[str, Any]]:
        """
        获取指定人员的任务列表
        
        Args:
            person_name: 人员姓名
            
        Returns:
            任务列表
        """
        try:
            # 获取任务数据
            result = await self.tapd_client.get_tasks(
                workspace_id=self.workspace_id,
                owner=person_name
            )

            if result.get("status") != 1:
                self.logger.error(f"获取 {person_name} 的任务失败: {result}")
                return []

            tasks_data = result.get("data", {})

            if isinstance(tasks_data, dict):
                tasks_list = list(tasks_data.values())
            else:
                tasks_list = tasks_data or []

            # 格式化任务数据
            formatted_tasks = []
            for task_wrapper in tasks_list:
                if isinstance(task_wrapper, dict):
                    # 提取内层的Task对象
                    if 'Task' in task_wrapper and isinstance(task_wrapper['Task'], dict):
                        task = task_wrapper['Task']
                        formatted_task = self._format_task_data(task)
                        formatted_tasks.append(formatted_task)
                    else:
                        # 直接是Task对象的情况
                        formatted_task = self._format_task_data(task_wrapper)
                        formatted_tasks.append(formatted_task)
                else:
                    self.logger.warning(f"跳过非dict任务: {type(task_wrapper)}")

            self.logger.info(f"获取 {person_name} 的任务数据成功: {len(formatted_tasks)} 个任务")
            return formatted_tasks
            
        except Exception as e:
            self.logger.error(f"获取 {person_name} 的任务数据失败: {e}")
            return []
    
    def _format_task_data(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化任务数据为标准格式
        
        Args:
            task: 原始任务数据
            
        Returns:
            格式化后的任务数据
        """
        # 处理状态
        status = task.get('status', '')
        formatted_status = format_status(status)
        
        # 处理优先级
        priority = task.get('priority', '中')
        formatted_priority = format_priority_for_display(priority)
        
        # 构建TAPD URL
        task_id = task.get('id', '')
        tapd_url = f"https://www.tapd.cn/{self.workspace_id}/prong/tasks/view/{task_id}" if task_id else ""
        
        # 处理进度
        progress = task.get('progress', '0')
        try:
            progress_value = int(float(progress))
        except (ValueError, TypeError):
            progress_value = 0
        
        return {
            'id': task_id,
            'name': task.get('name', ''),
            'status': formatted_status,
            'priority': formatted_priority,
            'due_date': task.get('due', ''),
            'description': task.get('description', ''),
            'story_id': task.get('story_id', ''),
            'created_time': task.get('created', ''),
            'owner': task.get('owner', ''),
            'progress': progress_value,
            'tapd_url': tapd_url
        }
    
    async def update_task_status(self, task_id: str, new_status: str) -> Dict[str, Any]:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            new_status: 新状态
            
        Returns:
            更新结果
        """
        try:
            # 将前端状态转换为TAPD状态
            from utils import format_status_for_tapd
            tapd_status = format_status_for_tapd(new_status)
            
            result = await self.tapd_client.update_task(
                workspace_id=self.workspace_id,
                task_id=task_id,
                status=tapd_status
            )
            
            if result.get("status") == 1:
                self.logger.info(f"任务状态更新成功: {task_id} -> {new_status}")
                return {"success": True, "message": "状态更新成功"}
            else:
                error_msg = result.get("info", "未知错误")
                self.logger.error(f"任务状态更新失败: {task_id}, 错误: {error_msg}")
                return {"success": False, "message": error_msg}
                
        except Exception as e:
            self.logger.error(f"更新任务状态异常: {e}")
            return {"success": False, "message": f"更新失败: {str(e)}"}
    
    async def batch_update_tasks(self, task_updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量更新任务
        
        Args:
            task_updates: 任务更新列表，格式：[{"task_id": "xxx", "status": "xxx"}, ...]
            
        Returns:
            批量更新结果
        """
        results = {
            "success_count": 0,
            "failed_count": 0,
            "details": []
        }
        
        for update in task_updates:
            task_id = update.get("task_id")
            new_status = update.get("status")
            
            if not task_id or not new_status:
                results["failed_count"] += 1
                results["details"].append({
                    "task_id": task_id,
                    "success": False,
                    "message": "任务ID或状态缺失"
                })
                continue
            
            result = await self.update_task_status(task_id, new_status)
            
            if result.get("success"):
                results["success_count"] += 1
            else:
                results["failed_count"] += 1
            
            results["details"].append({
                "task_id": task_id,
                "success": result.get("success", False),
                "message": result.get("message", "")
            })
        
        self.logger.info(f"批量更新任务完成: 成功 {results['success_count']} 个，失败 {results['failed_count']} 个")
        return results
    
    async def get_task_details(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务详细信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务详情
        """
        try:
            result = await self.tapd_client.get_task(
                workspace_id=self.workspace_id,
                task_id=task_id
            )
            
            if result.get("status") == 1:
                task_data = result.get("data", {})
                
                # 处理TAPD API返回的data可能是列表的情况
                if isinstance(task_data, list):
                    # 如果是列表，取第一个元素
                    if task_data:
                        task_data = task_data[0]
                    else:
                        task_data = {}
                elif isinstance(task_data, dict):
                    # 如果是字典，检查是否有Task键
                    if 'Task' in task_data:
                        task_data = task_data['Task']
                    elif len(task_data) == 1:
                        # 如果字典只有一个键，取第一个值
                        task_data = list(task_data.values())[0]
                
                # 确保task_data是字典
                if not isinstance(task_data, dict):
                    task_data = {}
                
                return {"success": True, "task": task_data}
            else:
                error_msg = result.get("info", "未知错误")
                return {"success": False, "message": error_msg}
                
        except Exception as e:
            self.logger.error(f"获取任务详情失败: {e}")
            return {"success": False, "message": f"获取失败: {str(e)}"}

    async def get_story_tasks(self, story_id: str) -> List[Dict[str, Any]]:
        """
        获取需求下的所有任务

        Args:
            story_id: 需求ID

        Returns:
            任务列表
        """
        try:
            result = await self.tapd_client.get_tasks(
                workspace_id=self.workspace_id,
                story_id=story_id
            )

            if result.get("status") == 1:
                tasks_data = result.get("data", {})

                # 处理返回数据格式
                if isinstance(tasks_data, dict):
                    tasks_list = list(tasks_data.values())
                else:
                    tasks_list = tasks_data or []

                # 格式化任务数据
                formatted_tasks = []
                for task_wrapper in tasks_list:
                    if isinstance(task_wrapper, dict):
                        # 提取内层的Task对象
                        if 'Task' in task_wrapper and isinstance(task_wrapper['Task'], dict):
                            task = task_wrapper['Task']
                            formatted_task = self._format_task_data(task)
                            formatted_tasks.append(formatted_task)
                        else:
                            # 直接是Task对象的情况
                            formatted_task = self._format_task_data(task_wrapper)
                            formatted_tasks.append(formatted_task)

                self.logger.info(f"获取需求 {story_id} 下的任务成功，共 {len(formatted_tasks)} 个任务")
                return formatted_tasks
            else:
                error_msg = result.get("info", "未知错误")
                self.logger.error(f"获取需求任务失败: {story_id}, 错误: {error_msg}")
                return []

        except Exception as e:
            self.logger.error(f"获取需求任务异常: {story_id}, 异常: {e}")
            return []

    async def create_story(self, story_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建TAPD需求
        
        Args:
            story_data: 需求数据
            
        Returns:
            创建结果
        """
        try:
            result = await self.tapd_client.create_story(
                workspace_id=self.workspace_id,
                name=story_data.get("name", ""),
                description=story_data.get("description", ""),
                priority=story_data.get("priority", "中"),
                status=story_data.get("status", "planning")
            )
            
            if result.get("status") == 1:
                story_id = self._extract_story_id(result)
                self.logger.info(f"TAPD需求创建成功: {story_id}")
                return {"success": True, "story_id": story_id}
            else:
                error_msg = result.get("info", "未知错误")
                self.logger.error(f"TAPD需求创建失败: {error_msg}")
                return {"success": False, "message": error_msg}
                
        except Exception as e:
            self.logger.error(f"创建TAPD需求异常: {e}")
            return {"success": False, "message": f"创建失败: {str(e)}"}

    async def create_ocr_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建OCR相关任务
        
        Args:
            task_data: 任务数据
            
        Returns:
            创建结果
        """
        try:
            result = await self.tapd_client.create_task(
                workspace_id=self.workspace_id,
                name=task_data.get("name", ""),
                description=task_data.get("description", ""),
                story_id=task_data.get("story_id"),
                owner=task_data.get("owner", ""),
                priority=task_data.get("priority", "中"),
                begin=task_data.get("begin"),
                due=task_data.get("due"),
                cc=task_data.get("cc"),
                status=task_data.get("status")
            )
            
            if result.get("status") == 1:
                task_id = self._extract_task_id(result)
                self.logger.info(f"OCR任务创建成功: {task_id}")
                return {"success": True, "task_id": task_id}
            else:
                error_msg = result.get("info", "未知错误")
                self.logger.error(f"OCR任务创建失败: {error_msg}")
                return {"success": False, "message": error_msg}
                
        except Exception as e:
            self.logger.error(f"创建OCR任务异常: {e}")
            return {"success": False, "message": f"创建失败: {str(e)}"}
    
    def _extract_task_id(self, task_result: Dict) -> Optional[str]:
        """从任务创建结果中提取任务ID"""
        try:
            if isinstance(task_result, dict):
                data = task_result.get("data", {})
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, dict) and value.get("id"):
                            return str(value["id"])
            return None
        except Exception:
            return None

    def _extract_story_id(self, story_result: Dict) -> Optional[str]:
        """从需求创建结果中提取需求ID"""
        try:
            if isinstance(story_result, dict):
                data = story_result.get("data", {})
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, dict) and value.get("id"):
                            return str(value["id"])
            return None
        except Exception:
            return None
    
    async def get_workflow_tasks(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取工作流相关的所有任务
        
        Returns:
            按负责人分组的任务字典
        """
        workflow_tasks = {}
        
        for role, person in self.responsible_persons.items():
            tasks = await self.get_person_tasks(person)
            workflow_tasks[person] = tasks
        
        return workflow_tasks
    
    async def get_task_statistics(self) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Returns:
            统计信息
        """
        all_tasks = []
        person_stats = {}
        
        # 获取所有负责人的任务
        for person in self.responsible_persons.values():
            tasks = await self.get_person_tasks(person)
            all_tasks.extend(tasks)
            
            # 统计个人任务状态
            person_stats[person] = {
                'total': len(tasks),
                'completed': len([t for t in tasks if t['status'] == '已完成']),
                'in_progress': len([t for t in tasks if t['status'] == '进行中']),
                'pending': len([t for t in tasks if t['status'] == '未开始'])
            }
        
        # 总体统计
        total_stats = {
            'total_tasks': len(all_tasks),
            'total_completed': len([t for t in all_tasks if t['status'] == '已完成']),
            'total_in_progress': len([t for t in all_tasks if t['status'] == '进行中']),
            'total_pending': len([t for t in all_tasks if t['status'] == '未开始']),
            'completion_rate': 0
        }
        
        if total_stats['total_tasks'] > 0:
            total_stats['completion_rate'] = round(
                (total_stats['total_completed'] / total_stats['total_tasks']) * 100, 1
            )
        
        return {
            'total_statistics': total_stats,
            'person_statistics': person_stats,
            'generated_at': datetime.now().isoformat()
        } 