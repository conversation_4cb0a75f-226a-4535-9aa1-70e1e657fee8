#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web应用主模块 - App层
Flask应用的配置和初始化
"""

import logging
from flask import Flask

from utils import ConfigLoader
from core import MonitorService


def create_app(config_loader: ConfigLoader = None) -> Flask:
    """
    创建并配置Flask应用
    
    Args:
        config_loader: 配置加载器实例
        
    Returns:
        配置好的Flask应用实例
    """
    # 初始化Flask应用
    app = Flask(__name__, 
                template_folder='../web/templates',
                static_folder='../web/static')
    
    # 基本配置
    app.secret_key = 'ocr_monitor_secret_key_v2'
    
    # 配置Flask的JSON编码，确保中文字符正常显示
    app.config['JSON_AS_ASCII'] = False
    app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True
    
    # 初始化配置和服务
    if config_loader is None:
        config_loader = ConfigLoader()
    
    # 验证配置
    validation_result = config_loader.validate_config()
    if not validation_result["is_valid"]:
        logging.error(f"配置验证失败: {validation_result['errors']}")
        for error in validation_result["errors"]:
            print(f"❌ 配置错误: {error}")
        raise ValueError("配置文件无效，请检查配置")
    
    # 显示配置警告
    for warning in validation_result.get("warnings", []):
        print(f"⚠️  配置警告: {warning}")
    
    # 初始化监控服务
    monitor_service = MonitorService(config_loader)
    
    # 将服务实例注册到应用上下文
    app.config['MONITOR_SERVICE'] = monitor_service
    app.config['CONFIG_LOADER'] = config_loader
    
    # 注册路由
    from .api_routes import register_api_routes
    from .views import register_view_routes
    
    register_api_routes(app)
    register_view_routes(app)
    
    # 设置日志
    setup_logging(app, config_loader)
    
    logging.info("Flask应用初始化完成")
    print("✅ Flask应用初始化完成")
    
    return app


def setup_logging(app: Flask, config_loader: ConfigLoader):
    """设置应用日志"""
    # 根据配置决定日志级别
    if config_loader.is_debug_mode():
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        app.logger.setLevel(logging.DEBUG)
    else:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        app.logger.setLevel(logging.INFO)


def get_monitor_service(app: Flask) -> MonitorService:
    """从Flask应用获取监控服务实例"""
    return app.config['MONITOR_SERVICE']


def get_config_loader(app: Flask) -> ConfigLoader:
    """从Flask应用获取配置加载器实例"""
    return app.config['CONFIG_LOADER'] 