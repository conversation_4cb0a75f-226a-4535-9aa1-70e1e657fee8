#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整OCR工作流的脚本
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils import ConfigLoader
from core import TaskStatusMonitor, TAPDService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_complete_workflow():
    """测试完整的OCR工作流"""
    print("🧪 开始测试完整OCR工作流")
    
    # 初始化服务
    config_loader = ConfigLoader()
    task_monitor = TaskStatusMonitor(config_loader)
    tapd_service = TAPDService(config_loader)
    
    print("✅ 服务初始化完成")
    
    # 选择一个现有的需求进行测试
    story_id = "1138183800001000907"  # 从之前的测试中获得
    
    print(f"🎯 测试需求: {story_id}")
    
    # 1. 获取需求下的所有任务
    story_tasks = await tapd_service.get_story_tasks(story_id)
    print(f"📋 需求下共有 {len(story_tasks)} 个任务:")
    for task in story_tasks:
        print(f"  - {task.get('name', '')} (状态: {task.get('status', '')}, ID: {task.get('id', '')})")
    
    # 2. 模拟添加标签创建后续任务
    print(f"\n⚡ 模拟为原始任务添加标签...")
    
    # 找到一个原始任务
    original_task = None
    for task in story_tasks:
        task_name = task.get('name', '')
        if ('准确性问题' in task_name or '完备性问题' in task_name):
            original_task = task
            break
    
    if original_task:
        task_id = original_task.get('id')
        print(f"📝 选择任务: {original_task.get('name', '')} (ID: {task_id})")
        
        # 模拟添加标签
        test_labels = ["accuracy_content_error"]  # 内容识别错误
        print(f"🏷️  添加标签: {test_labels}")
        
        # 触发标签添加事件
        await task_monitor.handle_task_tag_added(task_id, test_labels)
        
        print("✅ 标签添加处理完成")
        
        # 3. 重新获取需求下的任务，查看是否创建了后续任务
        print(f"\n🔍 重新检查需求下的任务...")
        updated_story_tasks = await tapd_service.get_story_tasks(story_id)
        print(f"📋 更新后共有 {len(updated_story_tasks)} 个任务:")
        
        followup_tasks = []
        for task in updated_story_tasks:
            task_name = task.get('name', '')
            print(f"  - {task_name} (状态: {task.get('status', '')}, ID: {task.get('id', '')})")
            
            # 检查是否是后续任务
            followup_rules = config_loader.get_ocr_config().get("followup_rules", [])
            followup_task_titles = [rule.get('new_task_title', '') for rule in followup_rules]
            if any(title in task_name for title in followup_task_titles if title):
                followup_tasks.append(task)
        
        if followup_tasks:
            print(f"✅ 成功创建了 {len(followup_tasks)} 个后续任务")
            
            # 4. 模拟完成所有任务
            print(f"\n⚡ 模拟完成所有任务...")
            
            # 完成原始任务
            print(f"📝 完成原始任务: {original_task.get('name', '')}")
            result = await tapd_service.update_task_status(task_id, "已完成")
            print(f"  结果: {result}")
            
            # 完成后续任务
            for followup_task in followup_tasks:
                followup_task_id = followup_task.get('id')
                print(f"📝 完成后续任务: {followup_task.get('name', '')}")
                result = await tapd_service.update_task_status(followup_task_id, "已完成")
                print(f"  结果: {result}")
            
            # 5. 触发批次完成检查
            print(f"\n⚡ 触发批次完成检查...")
            await task_monitor._check_single_batch_completion(story_id)
            
            # 6. 最终检查是否创建了重新OCR任务
            print(f"\n🔍 最终检查是否创建了重新OCR任务...")
            final_story_tasks = await tapd_service.get_story_tasks(story_id)
            print(f"📋 最终共有 {len(final_story_tasks)} 个任务:")
            
            reocr_tasks = []
            for task in final_story_tasks:
                task_name = task.get('name', '')
                print(f"  - {task_name} (状态: {task.get('status', '')}, ID: {task.get('id', '')})")
                
                if '重新OCR' in task_name:
                    reocr_tasks.append(task)
            
            if reocr_tasks:
                print(f"🎉 成功创建了 {len(reocr_tasks)} 个重新OCR任务!")
                for task in reocr_tasks:
                    print(f"  ✅ {task.get('name', '')} (负责人: {task.get('owner', '')})")
            else:
                print("❌ 没有创建重新OCR任务")
        else:
            print("❌ 没有创建后续任务")
    else:
        print("❌ 没有找到原始任务")
    
    print("\n✅ 测试完成")

if __name__ == '__main__':
    asyncio.run(test_complete_workflow())
