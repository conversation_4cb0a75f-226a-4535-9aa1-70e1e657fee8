#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证任务创建的脚本
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils import ConfigLoader
from core import TaskStatusMonitor, TAPDService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def verify_task_creation():
    """验证任务创建"""
    print("🔍 验证任务创建")
    
    # 初始化服务
    config_loader = ConfigLoader()
    tapd_service = TAPDService(config_loader)
    
    # 从之前的测试中获得的任务ID
    created_task_id = "1138183800001000919"  # 内容识别错误处理任务
    story_id = "1138183800001000907"
    
    print(f"📝 验证任务ID: {created_task_id}")
    
    # 1. 直接获取任务详情
    task_details = await tapd_service.get_task_details(created_task_id)
    if task_details.get("success"):
        task_data = task_details.get("task", {})
        print(f"✅ 任务存在: {task_data.get('name', '')} (状态: {task_data.get('status', '')})")
        print(f"   需求ID: {task_data.get('story_id', '')}")
        print(f"   负责人: {task_data.get('owner', '')}")
    else:
        print(f"❌ 任务不存在或获取失败: {task_details.get('message', '')}")
        return
    
    # 2. 等待一段时间后重新获取需求任务
    print(f"\n⏰ 等待5秒后重新获取需求任务...")
    time.sleep(5)
    
    story_tasks = await tapd_service.get_story_tasks(story_id)
    print(f"📋 需求 {story_id} 下共有 {len(story_tasks)} 个任务:")
    
    followup_tasks = []
    for task in story_tasks:
        task_name = task.get('name', '')
        task_id = task.get('id', '')
        print(f"  - {task_name} (状态: {task.get('status', '')}, ID: {task_id})")
        
        # 检查是否是后续任务
        followup_rules = config_loader.get_ocr_config().get("followup_rules", [])
        followup_task_titles = [rule.get('new_task_title', '') for rule in followup_rules]
        if any(title in task_name for title in followup_task_titles if title):
            followup_tasks.append(task)
            print(f"    ✅ 这是后续任务")
    
    print(f"\n📊 统计: 找到 {len(followup_tasks)} 个后续任务")
    
    # 3. 如果有后续任务，测试完成所有任务后的重新OCR创建
    if followup_tasks:
        print(f"\n⚡ 测试完成所有任务后创建重新OCR任务...")
        
        # 初始化任务状态监控
        task_monitor = TaskStatusMonitor(config_loader)
        
        # 模拟完成后续任务
        for followup_task in followup_tasks:
            followup_task_id = followup_task.get('id')
            task_name = followup_task.get('name', '')
            current_status = followup_task.get('status', '')
            
            if current_status != '已完成':
                print(f"📝 完成后续任务: {task_name}")
                result = await tapd_service.update_task_status(followup_task_id, "已完成")
                print(f"  结果: {result}")
        
        # 触发批次完成检查
        print(f"\n⚡ 触发批次完成检查...")
        await task_monitor._check_single_batch_completion(story_id)
        
        # 等待一段时间后检查是否创建了重新OCR任务
        print(f"\n⏰ 等待5秒后检查重新OCR任务...")
        time.sleep(5)
        
        final_story_tasks = await tapd_service.get_story_tasks(story_id)
        print(f"📋 最终需求 {story_id} 下共有 {len(final_story_tasks)} 个任务:")
        
        reocr_tasks = []
        for task in final_story_tasks:
            task_name = task.get('name', '')
            task_id = task.get('id', '')
            print(f"  - {task_name} (状态: {task.get('status', '')}, ID: {task_id})")
            
            if '重新OCR' in task_name:
                reocr_tasks.append(task)
                print(f"    🎉 这是重新OCR任务!")
        
        if reocr_tasks:
            print(f"\n🎉 成功! 创建了 {len(reocr_tasks)} 个重新OCR任务")
            for task in reocr_tasks:
                print(f"  ✅ {task.get('name', '')} (负责人: {task.get('owner', '')})")
        else:
            print(f"\n❌ 没有创建重新OCR任务")
    else:
        print(f"\n❌ 没有找到后续任务，无法测试重新OCR创建")
    
    print("\n✅ 验证完成")

if __name__ == '__main__':
    asyncio.run(verify_task_creation())
