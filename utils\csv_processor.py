#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV数据处理器 - Utils层
用于解析和处理CSV格式的OCR校验数据
"""

import json
import csv
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field, validator
import pandas as pd
from collections import defaultdict


class CSVOCRValidationRecord(BaseModel):
    """CSV格式的OCR校验记录模型"""
    bill_no: str = Field(..., description="借据号")
    approval_result: str = Field(..., description="审批结果")
    complete_dtl: Optional[str] = Field(default="", description="完备性详情")
    accuracy_dtl: Optional[str] = Field(default="", description="准确性详情")
    consistency_dtl: Optional[str] = Field(default="", description="一致性详情")
    
    @validator('bill_no')
    def validate_bill_no(cls, v):
        """验证并格式化借据号"""
        if 'E' in v or 'e' in v:
            try:
                bill_number = int(float(v))
                return str(bill_number)
            except (ValueError, OverflowError):
                return v
        return v
    
    @validator('complete_dtl', 'accuracy_dtl', 'consistency_dtl')
    def validate_json_array(cls, v):
        """验证JSON数组格式"""
        if not v or v.strip() == "":
            return ""
        
        try:
            parsed = json.loads(v)
            if isinstance(parsed, list):
                return v
            else:
                return json.dumps([v])
        except json.JSONDecodeError:
            return json.dumps([v])


class CSVProcessor:
    """CSV数据处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_csv_file(self, file_path: str) -> List[CSVOCRValidationRecord]:
        """
        解析CSV文件
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            解析后的记录列表
        """
        try:
            records = []
            
            # 尝试不同的编码格式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            # 标准化字段名
                            normalized_row = self._normalize_field_names(row)
                            record = CSVOCRValidationRecord(**normalized_row)
                            records.append(record)
                    
                    self.logger.info(f"CSV文件解析成功，编码: {encoding}，记录数: {len(records)}")
                    break
                    
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    self.logger.error(f"解析CSV文件失败 (编码 {encoding}): {e}")
                    continue
            
            if not records:
                raise ValueError("无法解析CSV文件")
            
            return records
            
        except Exception as e:
            self.logger.error(f"解析CSV文件失败: {e}")
            raise
    
    def _normalize_field_names(self, row: Dict[str, str]) -> Dict[str, str]:
        """标准化字段名"""
        field_mapping = {
            '借据号': 'bill_no',
            '单号': 'bill_no',
            'bill_no': 'bill_no',
            '审批结果': 'approval_result',
            '结果': 'approval_result',
            'approval_result': 'approval_result',
            '完备性详情': 'complete_dtl',
            '完备性': 'complete_dtl',
            'complete_dtl': 'complete_dtl',
            '准确性详情': 'accuracy_dtl',
            '准确性': 'accuracy_dtl',
            'accuracy_dtl': 'accuracy_dtl',
            '一致性详情': 'consistency_dtl',
            '一致性': 'consistency_dtl',
            'consistency_dtl': 'consistency_dtl'
        }
        
        normalized_row = {}
        for key, value in row.items():
            normalized_key = field_mapping.get(key.strip(), key.strip())
            normalized_row[normalized_key] = str(value).strip() if value else ""
        
        return normalized_row
    
    def extract_problems_from_csv(self, csv_file_path: str) -> Dict[str, Any]:
        """
        从CSV文件提取问题信息
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            问题信息字典
        """
        try:
            records = self.parse_csv_file(csv_file_path)
            
            problems = {
                'completeness_problems': [],
                'accuracy_problems': []
            }
            
            # 用于去重的字典
            completeness_dict = {}
            accuracy_dict = {}
            
            for record in records:
                bill_no = record.bill_no
                
                # 处理完备性问题（去重合并相同借据号）
                if record.complete_dtl and record.complete_dtl.strip():
                    if record.complete_dtl != "[]" and record.complete_dtl.lower() not in ["null", "none", ""]:
                        # 如果借据号已存在，跳过重复项
                        if bill_no not in completeness_dict:
                            completeness_dict[bill_no] = record.complete_dtl
                
                # 处理准确性问题（检查更多可能的字段名）
                # 首先检查审批结果字段
                approval_result = getattr(record, 'approval_result', '') or getattr(record, '审批结果', '') or getattr(record, '结果', '')
                
                # 检查accuracy和consistency字段
                accuracy_result = getattr(record, 'accuracy_result', '') or getattr(record, 'accuracy', '') or getattr(record, '准确性', '') or getattr(record, '准确性结果', '')
                consistency_result = getattr(record, 'consistency_result', '') or getattr(record, 'consistency', '') or getattr(record, '一致性', '') or getattr(record, '一致性结果', '')
                
                # 如果审批结果是不通过，或accuracy/consistency为N
                if (approval_result and approval_result.strip() in ['不通过', 'N', 'n']) or \
                   (accuracy_result and accuracy_result.strip().upper() == 'N') or \
                   (consistency_result and consistency_result.strip().upper() == 'N'):
                    
                    # 从多个可能的字段获取问题详情
                    problem_detail = ''
                    consistency_dtl = getattr(record, 'consistency_dtl', '') or getattr(record, '一致性详情', '') or getattr(record, '一致性dtl', '')
                    accuracy_dtl = getattr(record, 'accuracy_dtl', '') or getattr(record, '准确性详情', '') or getattr(record, '准确性dtl', '')
                    
                    if consistency_dtl and consistency_dtl.strip():
                        problem_detail = consistency_dtl
                    elif accuracy_dtl and accuracy_dtl.strip():
                        problem_detail = accuracy_dtl
                    else:
                        problem_detail = f"审批结果: {approval_result}"
                    
                    if problem_detail and problem_detail != "[]" and problem_detail.lower() not in ["null", "none", ""]:
                        # 如果借据号已存在，跳过重复项
                        if bill_no not in accuracy_dict:
                            accuracy_dict[bill_no] = problem_detail
            
            # 转换为列表格式
            for bill_no, problem_detail in completeness_dict.items():
                problems['completeness_problems'].append({
                    'bill_no': bill_no,
                    'problem_detail': problem_detail,
                    'type': 'completeness'
                })
            
            for bill_no, problem_detail in accuracy_dict.items():
                problems['accuracy_problems'].append({
                    'bill_no': bill_no,
                    'problem_detail': problem_detail,
                    'type': 'accuracy'
                })
            
            self.logger.info(f"从CSV提取问题：完备性 {len(problems['completeness_problems'])} 个，"
                           f"准确性 {len(problems['accuracy_problems'])} 个")
            
            return problems
            
        except Exception as e:
            self.logger.error(f"从CSV提取问题失败: {e}")
            raise
    
    def group_problems_by_type(self, problems: Dict[str, Any]) -> Dict[str, Any]:
        """
        按问题类型分组
        
        Args:
            problems: 问题字典
            
        Returns:
            分组后的问题字典
        """
        grouped_problems = {
            'completeness_grouped': defaultdict(list),
            'accuracy_grouped': defaultdict(list)
        }
        
        # 完备性问题分组（按文件类型）
        for problem in problems.get('completeness_problems', []):
            file_type = problem.get('issue', '未知文件')
            grouped_problems['completeness_grouped'][file_type].append(problem)
        
        # 准确性问题分组（按问题类型）
        for problem in problems.get('accuracy_problems', []):
            issue_type = problem.get('issue', '未知问题')
            grouped_problems['accuracy_grouped'][issue_type].append(problem)
        
        return grouped_problems
    
    def generate_task_descriptions(self, grouped_problems: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成任务描述
        
        Args:
            grouped_problems: 分组的问题字典
            
        Returns:
            任务描述字典
        """
        task_descriptions = {
            'completeness_tasks': [],
            'accuracy_tasks': []
        }
        
        # 生成完备性任务（只生成一个任务，包含所有完备性问题）
        completeness_grouped = grouped_problems.get('completeness_grouped', {})
        if completeness_grouped:
            all_bill_nos = []
            all_file_types = set()
            
            # 收集所有借据号和文件类型
            for file_type, problems in completeness_grouped.items():
                bill_nos = [p['bill_no'] for p in problems]
                all_bill_nos.extend(bill_nos)
                all_file_types.add(file_type)
            
            # 只创建一个完备性任务
            task_descriptions['completeness_tasks'].append({
                'title': "完备性问题处理",
                'description': f"缺失文件类型：{', '.join(sorted(all_file_types))}\\n涉及借据：{', '.join(all_bill_nos[:10])}{'...' if len(all_bill_nos) > 10 else ''}\\n总计：{len(all_bill_nos)} 个借据，{len(all_file_types)} 种文件类型",
                'bill_count': len(all_bill_nos),
                'file_types': list(all_file_types)
            })
        
        # 生成准确性任务（按文件类型分别生成）
        for issue_type, problems in grouped_problems.get('accuracy_grouped', {}).items():
            bill_nos = [p['bill_no'] for p in problems]
            task_descriptions['accuracy_tasks'].append({
                'title': f"准确性问题-{issue_type}",
                'description': f"问题类型：{issue_type}\\n涉及借据：{', '.join(bill_nos)}",
                'bill_count': len(bill_nos),
                'issue_type': issue_type
            })
        
        return task_descriptions
    
    def analyze_csv_data(self, csv_file_path: str) -> Dict[str, Any]:
        """
        完整分析CSV数据
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            分析结果
        """
        try:
            # 提取问题
            problems = self.extract_problems_from_csv(csv_file_path)
            
            # 分组问题
            grouped_problems = self.group_problems_by_type(problems)
            
            # 生成任务描述
            task_descriptions = self.generate_task_descriptions(grouped_problems)
            
            # 统计信息
            stats = {
                'total_completeness_problems': len(problems.get('completeness_problems', [])),
                'total_accuracy_problems': len(problems.get('accuracy_problems', [])),
                'completeness_file_types': len(grouped_problems.get('completeness_grouped', {})),
                'accuracy_issue_types': len(grouped_problems.get('accuracy_grouped', {})),
                'processed_at': datetime.now().isoformat()
            }
            
            return {
                'problems': problems,
                'grouped_problems': grouped_problems,
                'task_descriptions': task_descriptions,
                'stats': stats
            }
            
        except Exception as e:
            self.logger.error(f"分析CSV数据失败: {e}")
            raise


def clean_bill_no(bill_no):
    """清理借据号格式"""
    try:
        # 处理科学计数法
        if 'E' in str(bill_no) or 'e' in str(bill_no):
            return str(int(float(bill_no)))
        return str(bill_no)
    except:
        return str(bill_no) 