#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控服务 - Core层
OCR任务监控的核心业务逻辑
"""

import logging
import asyncio
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from utils import ConfigLoader, data_cache, get_or_create_person_codes
from .tapd_service import TAPDService
from .ocr_service import OCRService


class MonitorService:
    """OCR监控核心服务"""
    
    def __init__(self, config_loader: Optional[ConfigLoader] = None):
        """
        初始化监控服务
        
        Args:
            config_loader: 配置加载器实例
        """
        self.logger = logging.getLogger(__name__)
        self.config_loader = config_loader or ConfigLoader()
        self.tapd_service = TAPDService(self.config_loader)
        self.ocr_service = OCRService(self.config_loader)
        
        # 获取配置
        self.responsible_persons = self.config_loader.get_responsible_persons()
        
        # 启动后台缓存刷新
        self._start_background_cache_refresh()
        
        self.logger.info("OCR监控服务初始化完成")
    
    def _start_background_cache_refresh(self):
        """启动后台缓存刷新任务"""
        def cache_refresh_worker():
            while True:
                try:
                    # 每5分钟完全刷新一次缓存
                    asyncio.run(self._refresh_all_cache())
                    threading.Event().wait(300)  # 等待5分钟
                except Exception as e:
                    self.logger.error(f"后台缓存刷新异常: {e}")
                    threading.Event().wait(60)  # 出错后等待1分钟再试
        
        refresh_thread = threading.Thread(target=cache_refresh_worker, daemon=True)
        refresh_thread.start()
        self.logger.info("后台缓存刷新任务已启动（每5分钟完全刷新）")
    
    async def _refresh_all_cache(self):
        """完全刷新所有缓存（清空并重新填充）"""
        try:
            self.logger.info("开始完全刷新缓存...")
            
            # 1. 完全清空所有缓存
            data_cache.clear()
            self.logger.info("所有缓存已清空")
            
            # 2. 重新预加载关键数据
            preload_count = 0
            
            # 预加载人员任务缓存
            for person in self.responsible_persons.values():
                try:
                    tasks = await self.tapd_service.get_person_tasks(person)
                    valid_tasks = self._filter_valid_tasks(tasks)
                    cache_key = f"person_tasks.{person}"
                    data_cache.set(cache_key, valid_tasks, 'person_tasks')
                    preload_count += 1
                    self.logger.debug(f"预加载 {person} 的任务缓存: {len(valid_tasks)} 个任务")
                except Exception as e:
                    self.logger.error(f"预加载 {person} 任务缓存失败: {e}")
            
            # 预加载流程总览
            try:
                overview_data = await self._generate_process_overview()
                data_cache.set("process_overview.overview", overview_data, 'process_overview')
                preload_count += 1
                self.logger.debug("预加载流程总览缓存")
            except Exception as e:
                self.logger.error(f"预加载流程总览缓存失败: {e}")
            
            # 预加载工作流状态
            try:
                workflow_result = await self.ocr_service.get_ocr_workflow_status()
                if workflow_result.get("success"):
                    workflow_data = {
                        "stages": workflow_result.get("stages", []),
                        "generated_at": workflow_result.get("generated_at")
                    }
                    data_cache.set("workflow_status.status", workflow_data, 'workflow_status')
                    preload_count += 1
                    self.logger.debug("预加载工作流状态缓存")
            except Exception as e:
                self.logger.error(f"预加载工作流状态缓存失败: {e}")
            
            self.logger.info(f"缓存完全刷新完成，预加载了 {preload_count} 个缓存项")
            
        except Exception as e:
            self.logger.error(f"完全刷新缓存失败: {e}")
    
    async def get_person_tasks(self, person_name: str) -> Dict[str, Any]:
        """
        获取指定人员的任务（带缓存）
        
        Args:
            person_name: 人员姓名
            
        Returns:
            包含任务列表的响应
        """
        try:
            # 检查缓存
            cache_key = f"person_tasks.{person_name}"
            cached_data = data_cache.get(cache_key, 'person_tasks')
            
            if cached_data:
                self.logger.info(f"从缓存获取 {person_name} 的任务数据")
                return {
                    "success": True,
                    "tasks": cached_data,
                    "from_cache": True,
                    "person": person_name
                }
            
            # 从TAPD获取最新数据
            tasks = await self.tapd_service.get_person_tasks(person_name)
            
            # 过滤无效任务
            valid_tasks = self._filter_valid_tasks(tasks)
            
            # 缓存数据
            data_cache.set(cache_key, valid_tasks, 'person_tasks')
            
            return {
                "success": True,
                "tasks": valid_tasks,
                "from_cache": False,
                "person": person_name
            }
            
        except Exception as e:
            self.logger.error(f"获取人员任务失败: {e}")
            return {
                "success": False,
                "message": f"获取失败: {str(e)}",
                "tasks": [],
                "person": person_name
            }
    
    async def get_process_overview(self) -> Dict[str, Any]:
        """
        获取流程总览数据（带缓存）
        
        Returns:
            流程总览信息
        """
        try:
            # 检查缓存
            cache_key = "process_overview.overview"
            cached_data = data_cache.get(cache_key, 'process_overview')
            
            if cached_data:
                return {"success": True, "data": cached_data, "from_cache": True}
            
            # 生成总览数据
            overview_data = await self._generate_process_overview()
            
            # 缓存数据
            data_cache.set(cache_key, overview_data, 'process_overview')
            
            return {"success": True, "data": overview_data, "from_cache": False}
            
        except Exception as e:
            self.logger.error(f"获取流程总览失败: {e}")
            return {"success": False, "message": f"获取失败: {str(e)}"}
    
    async def _generate_process_overview(self) -> Dict[str, Any]:
        """生成流程总览数据"""
        # 获取所有人员的任务
        all_tasks = []
        for person in self.responsible_persons.values():
            person_tasks = await self.tapd_service.get_person_tasks(person)
            all_tasks.extend(person_tasks)
        
        # 统计数据
        total_processes = len(all_tasks)
        completed_tasks = [t for t in all_tasks if t['status'] == '已完成']
        active_tasks = [t for t in all_tasks if t['status'] == '进行中']
        pending_tasks = [t for t in all_tasks if t['status'] == '未开始']
        
        # 检查逾期任务
        overdue_tasks = []
        current_date = datetime.now().date()
        
        for task in all_tasks:
            due_date_str = task.get('due_date', '')
            if due_date_str:
                try:
                    due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date()
                    if due_date < current_date and task['status'] != '已完成':
                        overdue_tasks.append(task)
                except ValueError:
                    continue
        
        # 最近活动
        recent_activities = []
        sorted_tasks = sorted(all_tasks, 
                             key=lambda x: x.get('created_time', ''), 
                             reverse=True)[:5]
        
        for task in sorted_tasks:
            activity = {
                'time': task.get('created_time', ''),
                'type': 'task_created',
                'description': f"{task.get('owner', '')}的任务: {task.get('name', '')}",
                'person': task.get('owner', '')
            }
            recent_activities.append(activity)
        
        return {
            'total_processes': total_processes,
            'active_processes': len(active_tasks),
            'completed_processes': len(completed_tasks),
            'pending_tasks': len(pending_tasks),
            'overdue_tasks': len(overdue_tasks),
            'recent_activities': recent_activities
        }
    
    async def get_workflow_status(self) -> Dict[str, Any]:
        """
        获取工作流状态（带缓存）
        
        Returns:
            工作流状态信息
        """
        try:
            # 检查缓存
            cache_key = "workflow_status.status"
            cached_data = data_cache.get(cache_key, 'workflow_status')
            
            if cached_data:
                return {"success": True, "data": cached_data, "from_cache": True}
            
            # 从OCR服务获取工作流状态
            workflow_result = await self.ocr_service.get_ocr_workflow_status()
            
            if workflow_result.get("success"):
                workflow_data = {
                    "stages": workflow_result.get("stages", []),
                    "generated_at": workflow_result.get("generated_at")
                }
                
                # 缓存数据
                data_cache.set(cache_key, workflow_data, 'workflow_status')
                
                return {"success": True, "data": workflow_data, "from_cache": False}
            else:
                return {"success": False, "message": workflow_result.get("message", "获取工作流状态失败")}
                
        except Exception as e:
            self.logger.error(f"获取工作流状态失败: {e}")
            return {"success": False, "message": f"获取失败: {str(e)}"}
    
    async def update_task_status(self, task_id: str, new_status: str, person_code: str = "", person_name: str = "") -> Dict[str, Any]:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            new_status: 新状态
            person_code: 操作人代码
            person_name: 操作人姓名
            
        Returns:
            更新结果
        """
        try:
            # 更新任务状态
            result = await self.tapd_service.update_task_status(task_id, new_status)
            
            if result.get("success"):
                # 记录操作日志
                if person_code and person_name:
                    from utils import log_user_action
                    log_user_action(
                        person_code=person_code,
                        person_name=person_name,
                        action="update_task_status",
                        details={
                            "task_id": task_id,
                            "new_status": new_status
                        }
                    )
                
                # 清除相关缓存
                await self._clear_related_cache(task_id)
                
                self.logger.info(f"任务状态更新成功: {task_id} -> {new_status}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"更新任务状态异常: {e}")
            return {"success": False, "message": f"更新失败: {str(e)}"}
    
    async def batch_update_tasks(self, task_updates: List[Dict[str, Any]], person_code: str = "", person_name: str = "") -> Dict[str, Any]:
        """
        批量更新任务状态
        
        Args:
            task_updates: 任务更新列表
            person_code: 操作人代码
            person_name: 操作人姓名
            
        Returns:
            批量更新结果
        """
        try:
            # 执行批量更新
            result = await self.tapd_service.batch_update_tasks(task_updates)
            
            if result.get("success_count", 0) > 0:
                # 记录操作日志
                if person_code and person_name:
                    from utils import log_user_action
                    log_user_action(
                        person_code=person_code,
                        person_name=person_name,
                        action="batch_update_tasks",
                        details={
                            "updates_count": len(task_updates),
                            "success_count": result.get("success_count", 0),
                            "failed_count": result.get("failed_count", 0)
                        }
                    )
                
                # 清除所有相关缓存
                for update in task_updates:
                    task_id = update.get("task_id")
                    if task_id:
                        await self._clear_related_cache(task_id)
            
            return result
            
        except Exception as e:
            self.logger.error(f"批量更新任务异常: {e}")
            return {"success": False, "message": f"批量更新失败: {str(e)}"}
    
    async def _clear_related_cache(self, task_id: str):
        """清除与任务相关的缓存"""
        try:
            # 清除所有人员任务缓存
            for person in self.responsible_persons.values():
                cache_key = f"person_tasks.{person}"
                data_cache.delete(cache_key)
            
            # 清除总览和工作流缓存
            data_cache.delete("process_overview.overview")
            data_cache.delete("workflow_status.status")
            
            self.logger.debug(f"已清除任务 {task_id} 相关的缓存")
            
        except Exception as e:
            self.logger.error(f"清除缓存失败: {e}")
    
    def get_config_data(self) -> Dict[str, Any]:
        """
        获取配置数据
        
        Returns:
            配置信息
        """
        try:
            # 获取负责人代码映射
            person_codes = get_or_create_person_codes(self.responsible_persons)
            
            return {
                "success": True,
                "ocr_validation": {
                    "responsible_persons": self.responsible_persons
                },
                "person_codes": person_codes
            }
            
        except Exception as e:
            self.logger.error(f"获取配置数据失败: {e}")
            return {"success": False, "message": f"获取失败: {str(e)}"}
    
    async def refresh_cache(self) -> Dict[str, Any]:
        """
        手动刷新缓存
        
        Returns:
            刷新结果
        """
        try:
            await self._refresh_all_cache()
            return {"success": True, "message": "缓存刷新成功"}
            
        except Exception as e:
            self.logger.error(f"手动刷新缓存失败: {e}")
            return {"success": False, "message": f"刷新失败: {str(e)}"}
    
    async def get_task_details(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务详情
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务详情
        """
        return await self.tapd_service.get_task_details(task_id)
    
    async def save_task_tags(self, task_id: str, tags: List[str], person_code: str = "", person_name: str = "") -> Dict[str, Any]:
        """
        保存任务标签
        
        Args:
            task_id: 任务ID
            tags: 标签列表
            person_code: 操作人代码
            person_name: 操作人姓名
            
        Returns:
            保存结果
        """
        try:
            # 验证标签
            validation_result = self.ocr_service.validate_problem_tags(tags)
            if not validation_result.get("is_valid"):
                return {
                    "success": False,
                    "message": f"标签验证失败: {', '.join(validation_result.get('warnings', []))}"
                }
            
            # 保存标签
            result = await self.ocr_service.save_task_problem_tags(task_id, tags)
            
            if result.get("success"):
                # 记录操作日志
                if person_code and person_name:
                    from utils import log_user_action
                    log_user_action(
                        person_code=person_code,
                        person_name=person_name,
                        action="save_task_tags",
                        details={
                            "task_id": task_id,
                            "tags": tags
                        }
                    )
                
                # 清除相关缓存
                await self._clear_related_cache(task_id)
            
            return result
            
        except Exception as e:
            self.logger.error(f"保存任务标签异常: {e}")
            return {"success": False, "message": f"保存失败: {str(e)}"}
    
    async def get_task_tags(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务标签
        
        Args:
            task_id: 任务ID
            
        Returns:
            标签信息
        """
        return await self.ocr_service.get_task_problem_tags(task_id)
    
    async def get_task_problem_tags(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务问题标签（别名方法）
        
        Args:
            task_id: 任务ID
            
        Returns:
            标签信息
        """
        return await self.ocr_service.get_task_problem_tags(task_id)
    
    def get_ocr_problem_types(self) -> List[Dict[str, Any]]:
        """获取OCR问题类型列表"""
        return self.ocr_service.get_ocr_problem_types()
    
    async def get_tapd_task_fields(self) -> Dict[str, Any]:
        """
        获取TAPD任务字段配置，包括状态和优先级选项
        
        Returns:
            包含字段配置的字典
        """
        try:
            # 尝试从TAPD API获取字段配置
            workspace_id = self.config_loader.get_tapd_config().get("workspace_id")
            
            if not workspace_id:
                raise ValueError("缺少workspace_id配置")
            
            # 调用TAPD客户端获取字段配置
            result = await self.tapd_service.tapd_client.get_task_fields(workspace_id)
            
            if result.get('success'):
                fields_data = result['data']
                
                # 提取状态和优先级配置
                status_options = []
                priority_options = []
                
                if 'data' in fields_data and fields_data['data']:
                    for field_group in fields_data['data']:
                        if 'TaskFieldsSettings' in field_group:
                            field_info = field_group['TaskFieldsSettings']
                            field_name = field_info.get('field_name', '')
                            
                            # 处理状态字段
                            if field_name == 'status':
                                options_str = field_info.get('options', '')
                                if options_str:
                                    # 解析状态选项
                                    for option in options_str.split('|'):
                                        if '=' in option:
                                            value, label = option.split('=', 1)
                                            status_options.append({
                                                'value': value,
                                                'label': label,
                                                'tapd_value': value
                                            })
                            
                            # 处理优先级字段
                            elif field_name == 'priority' or field_name == 'priority_label':
                                options_str = field_info.get('options', '')
                                if options_str:
                                    # 解析优先级选项
                                    for option in options_str.split('|'):
                                        if '=' in option:
                                            value, label = option.split('=', 1)
                                            priority_options.append({
                                                'value': value,
                                                'label': label,
                                                'tapd_value': value
                                            })
                
                # 如果没有从API获取到配置，使用TAPD标准配置作为备用
                if not status_options:
                    status_options = [
                        {'value': 'open', 'label': '未开始', 'tapd_value': 'open'},
                        {'value': 'progressing', 'label': '进行中', 'tapd_value': 'progressing'},
                        {'value': 'done', 'label': '已完成', 'tapd_value': 'done'}
                    ]
                
                if not priority_options:
                    # 使用TAPD推荐的priority_label字段的常见值
                    priority_options = [
                        {'value': '高', 'label': '高', 'tapd_value': '高'},
                        {'value': '中', 'label': '中', 'tapd_value': '中'},
                        {'value': '低', 'label': '低', 'tapd_value': '低'}
                    ]
                
                return {
                    'success': True,
                    'status_options': status_options,
                    'priority_options': priority_options,
                    'data_source': 'TAPD API',
                    'updated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
            else:
                # API调用失败，返回标准配置
                self.logger.warning("TAPD API调用失败，使用标准配置")
                return self._get_fallback_field_config('API调用失败')
                
        except Exception as e:
            self.logger.error(f"获取TAPD任务字段配置失败: {e}")
            # 返回备用配置
            return self._get_fallback_field_config(str(e))
    
    def _get_fallback_field_config(self, error_reason: str = "未知错误") -> Dict[str, Any]:
        """获取备用字段配置"""
        return {
            'success': True,
            'status_options': [
                {'value': 'open', 'label': '未开始', 'tapd_value': 'open'},
                {'value': 'progressing', 'label': '进行中', 'tapd_value': 'progressing'},
                {'value': 'done', 'label': '已完成', 'tapd_value': 'done'}
            ],
            'priority_options': [
                {'value': '高', 'label': '高', 'tapd_value': '高'},
                {'value': '中', 'label': '中', 'tapd_value': '中'},
                {'value': '低', 'label': '低', 'tapd_value': '低'}
            ],
            'data_source': 'fallback',
            'updated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'error': error_reason
        }
    
    def _filter_valid_tasks(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤有效任务（已禁用，直接返回所有任务）
        
        Args:
            tasks: 任务列表
            
        Returns:
            所有任务（不过滤）
        """
        if not isinstance(tasks, list):
            return []
        
        self.logger.info(f"任务过滤已禁用，直接返回所有 {len(tasks)} 个任务")
        return tasks 