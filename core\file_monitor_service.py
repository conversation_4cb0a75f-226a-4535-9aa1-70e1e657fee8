#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件监控服务 - Core层
监控ocr_doublecheck目录的文件变化，自动处理新增的CSV文件
"""

import os
import asyncio
import logging
import threading
import json
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    # 创建占位符类，防止导入错误
    class FileSystemEventHandler:
        pass
    class Observer:
        pass

from utils import ConfigLoader
from .ocr_service import OCRService
from .tapd_service import TAPDService

# 处理历史记录文件
PROCESSED_FILES_RECORD = "data/cache/processed_files.json"

# 全局处理锁
_processing_lock = threading.Lock()
_currently_processing = set()


class OCRFileEventHandler(FileSystemEventHandler):
    """OCR文件事件处理器"""
    
    def __init__(self, file_monitor_service):
        super().__init__()
        self.file_monitor_service = file_monitor_service
        self.logger = logging.getLogger(__name__)
    
    def on_moved(self, event):
        """文件移动事件 - 只处理移动到监控目录的文件"""
        if event.is_directory:
            return
        
        dest_path = event.dest_path
        if self._is_supported_file(dest_path):
            # 延迟处理，确保文件写入完成
            threading.Timer(2.0, self._process_new_file, args=[dest_path]).start()
    
    def _is_supported_file(self, file_path: str) -> bool:
        """检查是否为支持的文件类型"""
        supported_extensions = ['.csv', '.xlsx', '.xls']
        return any(file_path.lower().endswith(ext) for ext in supported_extensions)
    
    def _process_new_file(self, file_path: str):
        """处理新文件"""
        try:
            # 在新的事件循环中运行异步任务
            asyncio.run(self.file_monitor_service.process_new_file(file_path))
        except Exception as e:
            self.logger.error(f"处理新文件失败: {file_path}, 错误: {e}")


class FileMonitorService:
    """文件监控服务"""
    
    def __init__(self, config_loader: Optional[ConfigLoader] = None):
        """
        初始化文件监控服务
        
        Args:
            config_loader: 配置加载器实例
        """
        self.logger = logging.getLogger(__name__)
        self.config_loader = config_loader or ConfigLoader()
        
        # 初始化服务
        self.ocr_service = OCRService(self.config_loader)
        self.tapd_service = TAPDService(self.config_loader)
        
        # 监控配置
        self.monitor_directory = "ocr_doublecheck"
        self.is_monitoring = False
        self.observer = None
        self.event_handler = None
        
        # 处理历史记录 - 加载已处理的文件列表
        self.processed_files = self._load_processed_files()
        
        # 检查依赖
        if not WATCHDOG_AVAILABLE:
            self.logger.warning("watchdog库未安装，文件监控功能不可用")
        
        self.logger.info(f"文件监控服务初始化完成，已加载 {len(self.processed_files)} 个处理记录")
    
    def _load_processed_files(self) -> set:
        """从文件加载已处理的文件列表"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(PROCESSED_FILES_RECORD), exist_ok=True)
            
            if os.path.exists(PROCESSED_FILES_RECORD):
                with open(PROCESSED_FILES_RECORD, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return set(data)
                    elif isinstance(data, dict):
                        # 兼容旧格式，取files字段
                        return set(data.get('files', []))
            
            return set()
        except Exception as e:
            self.logger.warning(f"加载处理记录失败: {e}，使用空记录")
            return set()
    
    def _save_processed_files(self):
        """保存已处理的文件列表到文件"""
        try:
            os.makedirs(os.path.dirname(PROCESSED_FILES_RECORD), exist_ok=True)
            
            data = {
                'files': list(self.processed_files),
                'last_updated': datetime.now().isoformat(),
                'total_count': len(self.processed_files)
            }
            
            with open(PROCESSED_FILES_RECORD, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存处理记录失败: {e}")
    
    def _add_processed_file(self, file_path: str):
        """添加已处理的文件并立即保存"""
        self.processed_files.add(file_path)
        self._save_processed_files()
    
    def start_monitoring(self) -> bool:
        """
        启动文件监控
        
        Returns:
            是否启动成功
        """
        if not WATCHDOG_AVAILABLE:
            self.logger.error("watchdog库未安装，无法启动文件监控")
            return False
        
        if self.is_monitoring:
            return True
        
        try:
            # 确保监控目录存在
            monitor_path = Path(self.monitor_directory)
            monitor_path.mkdir(exist_ok=True)
            
            # 创建事件处理器和观察者
            self.event_handler = OCRFileEventHandler(self)
            self.observer = Observer()
            self.observer.schedule(
                self.event_handler,
                str(monitor_path),
                recursive=False
            )
            
            # 启动观察者
            self.observer.start()
            self.is_monitoring = True
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动文件监控失败: {e}")
            return False
    
    def stop_monitoring(self) -> bool:
        """
        停止文件监控
        
        Returns:
            是否停止成功
        """
        if not self.is_monitoring:
            return True
        
        try:
            if self.observer:
                self.observer.stop()
                self.observer.join()
                self.observer = None
            
            self.event_handler = None
            self.is_monitoring = False
            
            return True
            
        except Exception as e:
            self.logger.error(f"停止文件监控失败: {e}")
            return False
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """
        获取监控状态
        
        Returns:
            监控状态信息
        """
        return {
            "is_monitoring": self.is_monitoring,
            "monitor_directory": self.monitor_directory,
            "watchdog_available": WATCHDOG_AVAILABLE,
            "processed_files_count": len(self.processed_files),
            "observer_alive": self.observer.is_alive() if self.observer else False
        }
    
    async def process_new_file(self, file_path: str):
        """
        处理新检测到的文件
        
        Args:
            file_path: 文件路径
        """
        # 规范化路径
        normalized_path = os.path.normpath(file_path)
        
        # 检查是否已处理过
        if normalized_path in self.processed_files:
            return
        
        # 立即标记为已处理，防止重复
        self._add_processed_file(normalized_path)
        
        try:
            # 检查文件是否存在且可读
            if not os.path.isfile(normalized_path):
                return
            
            # 分析CSV文件
            analysis_result = await self.ocr_service.process_csv_validation_data(normalized_path)
            
            if not analysis_result.get("success"):
                self.logger.error(f"CSV文件分析失败: {analysis_result.get('message')}")
                return
            
            # 获取分析结果
            analysis_data = analysis_result.get("analysis_result", {})
            
            # 检查是否有问题需要处理
            if not self._has_issues_to_process(analysis_data):
                return
            
            # 创建TAPD需求
            story_result = await self._create_ocr_story(normalized_path, analysis_data)
            
            if not story_result.get("success"):
                self.logger.error(f"创建TAPD需求失败: {story_result.get('message')}")
                return
            
            story_id = story_result.get("story_id")
            
            # 创建相关任务
            task_result = await self._create_tasks_for_story(story_id, normalized_path, analysis_data)
            
            if task_result.get("success_count", 0) == 0:
                self.logger.error("未创建任何任务")
            
        except Exception as e:
            self.logger.error(f"处理文件异常: {file_path}, 错误: {e}")
            # 异常时从已处理列表中移除，允许重试
            if normalized_path in self.processed_files:
                self.processed_files.remove(normalized_path)
                self._save_processed_files()
    
    def _has_issues_to_process(self, analysis_data: Dict[str, Any]) -> bool:
        """检查是否有需要处理的问题"""
        try:
            # 检查分析结果中的统计信息
            stats = analysis_data.get("stats", {})
            
            # 检查是否有完备性或准确性问题
            completeness_issues = stats.get("total_completeness_problems", 0)
            accuracy_issues = stats.get("total_accuracy_problems", 0)
            
            return completeness_issues > 0 or accuracy_issues > 0
            
        except Exception as e:
            self.logger.error(f"检查问题数据失败: {e}")
            return False
    
    async def _create_ocr_story(self, file_path: str, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建OCR需求
        
        Args:
            file_path: 文件路径
            analysis_data: 分析结果
            
        Returns:
            创建结果
        """
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 构建需求标题：任务创建当前时间+ocr失败问题
            story_title = f"任务创建{current_time}+ocr失败问题"
            
            # 构建需求描述（总结性的OCR详细内容）
            description = self._build_story_description(file_path, analysis_data, current_time)
            
            # 创建需求（负责人为空）
            story_data = {
                "name": story_title,
                "description": description,
                "priority": "中",
                "status": "planning",
                "owner": ""  # 负责人为空
            }
            
            result = await self.tapd_service.create_story(story_data)
            return result
            
        except Exception as e:
            self.logger.error(f"创建OCR需求失败: {e}")
            return {"success": False, "message": f"创建失败: {str(e)}"}
    
    def _build_story_description(self, file_path: str, analysis_data: Dict[str, Any], current_time: str) -> str:
        """构建需求描述（总结性的OCR详细内容）"""
        try:
            file_name = os.path.basename(file_path)
            stats = analysis_data.get("stats", {})
            
            # 基本统计信息
            total_records = stats.get("total_records", 0)
            completeness_issues = stats.get("total_completeness_problems", 0)
            accuracy_issues = stats.get("total_accuracy_problems", 0)
            
            description = f"""
<p><b>OCR校验异常总结</b></p>
<p>来源文件: {file_name}</p>
<p>处理时间: {current_time}</p>
<p>总记录数: {total_records} 条</p>
<p>发现问题: 完备性问题 {completeness_issues} 个，准确性问题 {accuracy_issues} 个</p>
<p>需要人工处理的OCR失败问题，请按照工作流程进行处理。</p>
"""
            
            return description
            
        except Exception as e:
            self.logger.error(f"构建需求描述失败: {e}")
            return f"OCR校验异常处理，来源文件: {os.path.basename(file_path)}，创建时间: {current_time}"
    
    async def _create_tasks_for_story(self, story_id: str, file_path: str, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        为需求创建任务
        
        Args:
            story_id: 需求ID
            file_path: 文件路径
            analysis_data: 分析结果
            
        Returns:
            任务创建结果
        """
        try:
            result = {
                "success_count": 0,
                "failed_count": 0,
                "created_tasks": []
            }
            
            stats = analysis_data.get("stats", {})
            
            # 收集需要通知的负责人信息
            notification_persons = set()
            
            # 1. 创建完备性问题处理任务（只生成一个任务）
            completeness_issues = stats.get("total_completeness_problems", 0)
            if completeness_issues > 0:
                task_result = await self._create_completeness_task(story_id, analysis_data)
                if task_result.get("success"):
                    result["success_count"] += 1
                    result["created_tasks"].append(task_result["task_data"])
                    # 记录需要通知的负责人
                    actual_person = self.config_loader.get_actual_person_by_role("completeness")
                    if actual_person:
                        notification_persons.add(actual_person)
                else:
                    result["failed_count"] += 1
            
            # 2. 创建准确性问题判断任务（按文件类型分别生成）
            accuracy_issues = stats.get("total_accuracy_problems", 0)
            if accuracy_issues > 0:
                task_results = await self._create_accuracy_tasks(story_id, analysis_data)
                for task_result in task_results:
                    if task_result.get("success"):
                        result["success_count"] += 1
                        result["created_tasks"].append(task_result["task_data"])
                    else:
                        result["failed_count"] += 1
                
                # 记录需要通知的负责人
                if any(task_result.get("success") for task_result in task_results):
                    actual_person = self.config_loader.get_actual_person_by_role("accuracy")
                    if actual_person:
                        notification_persons.add(actual_person)
            
            # 为所有涉及的负责人创建一个统一的通知任务
            if notification_persons:
                await self._create_unified_notification_task(story_id, list(notification_persons))
            
            return result
            
        except Exception as e:
            self.logger.error(f"创建任务失败: {e}")
            return {"success_count": 0, "failed_count": 0, "created_tasks": []}
    
    async def _create_completeness_task(self, story_id: str, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建完备性问题处理任务（只生成一个任务）"""
        try:
            # 正确访问问题数据
            problems = analysis_data.get("problems", {})
            completeness_issues = problems.get("completeness_problems", [])
            
            # 构建任务内容：逐行显示借据号+完备性dtl
            task_description = "<p><b>完备性问题处理</b></p>\n"
            task_description += '<table border="1" cellpadding="4" cellspacing="0">\n'
            task_description += '<tr><th>借据号</th><th>缺失文件详情</th></tr>\n'
            
            for issue in completeness_issues:
                bill_no = issue.get("bill_no", "")
                problem_detail = issue.get("problem_detail", "")
                task_description += f'<tr><td>{bill_no}</td><td>{problem_detail}</td></tr>\n'
            
            task_description += '</table>\n'
            task_description += f'<p>总计: {len(completeness_issues)} 个借据存在完备性问题</p>'
            
            # 创建任务
            completeness_role = self.config_loader.get_role_name_by_role("completeness")
            
            task_data = {
                "name": "完备性问题处理",
                "description": task_description,
                "story_id": story_id,
                "owner": completeness_role,
                "priority": "中"
            }
            
            result = await self.tapd_service.create_ocr_task(task_data)
            
            if result.get("success"):
                task_id = result.get("task_id")
                return {
                    "success": True,
                    "task_data": {
                        "task_id": task_id,
                        "name": task_data["name"],
                        "type": "completeness",
                        "owner": completeness_role
                    }
                }
            else:
                self.logger.error(f"完备性任务创建失败: {result.get('message')}")
                return {"success": False, "message": result.get("message")}
                
        except Exception as e:
            self.logger.error(f"创建完备性任务失败: {e}")
            return {"success": False, "message": f"创建失败: {str(e)}"}
    
    async def _create_accuracy_tasks(self, story_id: str, analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建准确性问题判断任务（按文件类型分别生成）"""
        try:
            # 正确访问问题数据
            problems = analysis_data.get("problems", {})
            accuracy_issues = problems.get("accuracy_problems", [])
            
            if not accuracy_issues:
                return []
            
            # 按文件类型分组统计
            file_type_issues = {}
            
            for issue in accuracy_issues:
                problem_detail = issue.get("problem_detail", "")
                bill_no = issue.get("bill_no", "")
                
                # 解析文件类型（从问题详情中提取）
                file_type = self._extract_file_type_from_problem(problem_detail)
                
                if file_type not in file_type_issues:
                    file_type_issues[file_type] = []
                
                file_type_issues[file_type].append({
                    "bill_no": bill_no,
                    "detail": problem_detail
                })
            
            # 为每种文件类型创建任务
            task_results = []
            
            for file_type, issues in file_type_issues.items():
                task_name = f"准确性问题-{file_type}"
                
                task_description = f"<p><b>准确性问题判断 - {file_type}</b></p>\n"
                task_description += '<table border="1" cellpadding="4" cellspacing="0">\n'
                task_description += '<tr><th>借据号</th><th>问题详情</th></tr>\n'
                
                for issue in issues:
                    task_description += f'<tr><td>{issue["bill_no"]}</td><td>{issue["detail"]}</td></tr>\n'
                
                task_description += '</table>\n'
                task_description += f'<p>总计: {len(issues)} 个借据存在准确性问题</p>'
                
                accuracy_role = self.config_loader.get_role_name_by_role("accuracy")
                
                task_data = {
                    "name": task_name,
                    "description": task_description,
                    "story_id": story_id,
                    "owner": accuracy_role,
                    "priority": "中"
                }
                
                result = await self.tapd_service.create_ocr_task(task_data)
                
                if result.get("success"):
                    task_id = result.get("task_id")
                    task_results.append({
                        "success": True,
                        "task_data": {
                            "task_id": task_id,
                            "name": task_data["name"],
                            "type": "accuracy",
                            "owner": accuracy_role
                        }
                    })
                else:
                    self.logger.error(f"准确性任务创建失败: {result.get('message')}")
                    task_results.append({"success": False, "message": result.get("message")})
            
            return task_results
            
        except Exception as e:
            self.logger.error(f"创建准确性任务失败: {e}")
            return []
    
    async def _create_unified_notification_task(self, story_id: str, notification_persons: List[str]):
        """
        为涉及的负责人创建统一的通知任务
        
        Args:
            story_id: 需求ID
            notification_persons: 需要通知的负责人列表
        """
        try:
            if not notification_persons:
                return
            
            # 获取本地IP和端口
            local_ip = self.config_loader.get_local_ip()
            host, port = self.config_loader.get_web_host_port()
            
            # 构建网页链接
            web_url = f"http://{local_ip}:{port}"
            
            # 构建负责人列表和个人看板链接
            persons_list = "、".join(notification_persons)
            
            # 为每个负责人生成个人看板链接
            person_links = []
            for person in notification_persons:
                person_link = f'<a href="{web_url}/dashboard/{person}" target="_blank">{person}的个人看板</a>'
                person_links.append(person_link)
            
            task_description = f"""
<h3>OCR异常处理提醒</h3>
<p><strong>需求ID:</strong> {story_id}</p>
<p><strong>创建时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>处理说明:</strong> 您有新的OCR异常任务需处理</p>
<p><strong>查看地址:</strong> {' | '.join(person_links)}</p>
"""
            
            # 将第一个负责人作为主要负责人，其他人作为抄送
            primary_owner = notification_persons[0]
            cc_list = notification_persons[1:] if len(notification_persons) > 1 else []
            
            # 使用TAPD标准状态值
            from utils import format_status_for_tapd
            completed_status = format_status_for_tapd("已完成")
            
            self.logger.info(f"创建统一通知任务，主要负责人: {primary_owner}, 状态: {completed_status}")
            
            notification_task_data = {
                "name": "您有新的OCR异常任务需处理",
                "description": task_description,
                "story_id": story_id,
                "owner": primary_owner,
                "cc": cc_list,
                "priority": "高",
                "status": completed_status  # 通知任务自动完成
            }
            
            result = await self.tapd_service.create_ocr_task(notification_task_data)
            
            if result.get("success"):
                notification_task_id = result.get("task_id")
                self.logger.info(f"成功创建统一通知任务: {notification_task_id}, 主要负责人: {primary_owner}, 抄送: {cc_list}")
            else:
                self.logger.error(f"创建统一通知任务失败: {result.get('message')}")
                
        except Exception as e:
            self.logger.error(f"创建统一通知任务失败: {e}")
    
    def _extract_file_type_from_problem(self, problem_detail: str) -> str:
        """从问题详情中提取文件类型"""
        try:
            # 常见文件类型映射
            file_type_mapping = {
                "租赁合同": "租赁合同",
                "车辆登记证": "车辆登记证",
                "身份证": "身份证",
                "驾驶证": "驾驶证",
                "营业执照": "营业执照",
                "银行卡": "银行卡",
                "C03": "C03",
                "C04": "C04",
                "C05": "C05"
            }
            
            problem_str = str(problem_detail).lower()
            
            for file_type, display_name in file_type_mapping.items():
                if file_type.lower() in problem_str:
                    return display_name
            
            # 如果无法识别，返回原始内容
            return str(problem_detail)[:20] if problem_detail else "未知类型"
            
        except Exception as e:
            self.logger.error(f"提取文件类型失败: {e}")
            return "未知类型"
    
    async def scan_existing_files(self):
        """扫描现有文件并处理（临时禁用，防止重复处理）"""
        return
    
    def __del__(self):
        """析构函数，确保监控停止"""
        if self.is_monitoring:
            self.stop_monitoring()