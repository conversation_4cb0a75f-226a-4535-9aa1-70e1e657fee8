#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视图路由模块 - App层
定义页面路由和视图控制器
"""

import logging
from flask import Flask, render_template, redirect, url_for, request, current_app

from .web_app import get_monitor_service, get_config_loader
from utils import get_or_create_person_codes


def register_view_routes(app: Flask):
    """注册所有视图路由"""
    
    @app.route('/')
    def index():
        """根目录 - 显示人员列表和对应网页链接"""
        try:
            config_loader = get_config_loader(current_app)
            responsible_persons = config_loader.get_responsible_persons()
            
            # 获取人员代码映射
            person_codes = get_or_create_person_codes(responsible_persons)
            person_to_code = person_codes.get('person_to_code', {})
            
            # 构造人员列表
            persons = []
            for person_name, person_code in person_to_code.items():
                persons.append({
                    'name': person_name,
                    'code': person_code
                })
            
            # 按姓名排序
            persons.sort(key=lambda x: x['name'])
            
            # 返回人员列表页面的HTML
            return f"""
            <html>
            <head>
                <title>OCR校验任务监控看板</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 50px; background-color: #f8f9fa; }}
                    .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                    h1 {{ color: #333; text-align: center; margin-bottom: 30px; }}
                    .person-list {{ list-style: none; padding: 0; }}
                    .person-item {{ margin: 15px 0; }}
                    .person-link {{ 
                        display: block; padding: 20px; background: #f8f9fa; 
                        text-decoration: none; color: #333; border-radius: 8px;
                        border-left: 4px solid #007bff; font-size: 16px;
                        transition: all 0.3s ease;
                    }}
                    .person-link:hover {{ 
                        background: #e9ecef; 
                        transform: translateX(5px);
                        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    }}
                    .person-code {{ color: #6c757d; font-size: 14px; }}
                    .footer {{ text-align: center; margin-top: 30px; color: #6c757d; font-size: 14px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>📋 OCR校验任务监控看板</h1>
                    <p style="text-align: center; color: #6c757d; margin-bottom: 30px;">请选择要查看的人员页面：</p>
                    <ul class="person-list">
                        {"".join([
                            f'''<li class="person-item">
                                <a href="/{person['code']}" class="person-link">
                                    <strong>{person['name']}</strong>
                                    <div class="person-code">代码: {person['code']}</div>
                                </a>
                            </li>'''
                            for person in persons
                        ])}
                    </ul>
                    
                    <div class="footer">
                        <p>🔄 点击人员姓名进入专属任务看板</p>
                    </div>
                </div>
            </body>
            </html>
            """
                                 
        except Exception as e:
            app.logger.error(f"渲染主页失败: {e}")
            return f"页面加载失败: {str(e)}", 500
    
    @app.route('/<person_code>')
    def personal_dashboard(person_code):
        """个人专属看板页面 - 包含任务和流程图"""
        try:
            config_loader = get_config_loader(current_app)
            responsible_persons = config_loader.get_responsible_persons()
            
            # 获取人员代码映射
            person_codes = get_or_create_person_codes(responsible_persons)
            code_to_person = person_codes.get('code_to_person', {})
            
            # 验证人员代码
            person_name = code_to_person.get(person_code)
            if not person_name:
                app.logger.warning(f"无效的人员代码: {person_code}")
                return f"""
                <html>
                <head><title>页面不存在</title></head>
                <body style="font-family: Arial; text-align: center; margin-top: 100px;">
                    <h1>⚠️ 页面不存在</h1>
                    <p>代码 '{person_code}' 对应的页面不存在。</p>
                    <a href="/" style="color: #007bff; text-decoration: none;">← 返回主页选择有效页面</a>
                </body>
                </html>
                """, 404
            
            # 确定当前用户的角色
            user_roles = []
            for role, name in responsible_persons.items():
                if name == person_name:
                    user_roles.append(role)
            
            return render_template('index.html',
                                 page_title=f"{person_name}的专属看板",
                                 is_personal_page=True,
                                 current_person=person_name,
                                 person_code=person_code,
                                 person_name=person_name,
                                 user_roles=user_roles,
                                 responsible_persons=responsible_persons,
                                 default_person_filter=person_name,
                                 show_process_flow=True)
                                 
        except Exception as e:
            app.logger.error(f"渲染个人页面失败: {e}")
            return f"""
            <html>
            <head><title>系统错误</title></head>
            <body style="font-family: Arial; text-align: center; margin-top: 100px;">
                <h1>❌ 系统错误</h1>
                <p>处理请求时发生错误，请稍后重试。</p>
                <a href="/" style="color: #007bff; text-decoration: none;">← 返回主页</a>
            </body>
            </html>
            """, 500
    
    # 错误页面处理
    @app.errorhandler(404)
    def page_not_found(error):
        """404错误页面"""
        return f"""
        <html>
        <head><title>页面不存在</title></head>
        <body style="font-family: Arial; text-align: center; margin-top: 100px;">
            <h1>⚠️ 页面不存在</h1>
            <p>您访问的页面不存在。</p>
            <a href="/" style="color: #007bff; text-decoration: none;">← 返回主页</a>
        </body>
        </html>
        """, 404
    
    @app.errorhandler(500)
    def internal_server_error(error):
        """500错误页面"""
        return f"""
        <html>
        <head><title>服务器错误</title></head>
        <body style="font-family: Arial; text-align: center; margin-top: 100px;">
            <h1>❌ 服务器错误</h1>
            <p>服务器内部错误，请稍后重试。</p>
            <a href="/" style="color: #007bff; text-decoration: none;">← 返回主页</a>
        </body>
        </html>
        """, 500 