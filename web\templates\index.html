<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR校验任务监控看板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        /* 顶部进度区域样式 */
        .progress-section {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 10px 0;
            margin-bottom: 10px;
            border-radius: 0 0 8px 8px;
        }
        
        .progress-card {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 6px;
            padding: 8px;
            margin-bottom: 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .progress-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #4CAF50 var(--progress), rgba(255,255,255,0.3) var(--progress));
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            position: relative;
        }
        
        .progress-circle::before {
            content: '';
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .progress-text {
            position: relative;
            z-index: 1;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        /* 任务状态样式简化 */
        .task-status-card {
            border-radius: 15px;
            transition: transform 0.3s ease;
            height: 100%;
        }
        
        .task-status-card:hover {
            transform: translateY(-5px);
        }
        
        .status-未开始 {
            background: linear-gradient(135deg, #495057, #343a40);
            color: white;
        }
        
        .status-进行中 {
            background: linear-gradient(135deg, #0077be, #005c91);
            color: white;
        }
        
        .status-已完成 {
            background: linear-gradient(135deg, #146c43, #0d5233);
            color: white;
        }
        
        /* 任务详情区域 */
        .task-details-section {
            background: white;
            border-radius: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .task-item {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #dee2e6;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .task-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(5px);
        }
        
        .task-item.status-未开始 {
            border-left-color: #6c757d;
            background: #ffffff;
        }
        
        .task-item.status-进行中 {
            border-left-color: #0d6efd;
            background: #ffffff;
            border-left-width: 6px;
        }
        
        .task-item.status-已完成 {
            border-left-color: #198754;
            background: #ffffff;
            border-left-width: 6px;
        }
        
        /* 强制所有任务项目内的文字使用深色 */
        .task-item, .task-item * {
            color: #2c3e50 !important;
        }
        
        .task-item .task-meta span {
            color: #6c757d !important;
        }
        
        .task-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            font-size: 14px;
            color: #6c757d;
        }
        
        .task-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .priority-badge {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 500;
        }
        
        /* 过滤器样式 */
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        /* 改善文字对比度 */
        .progress-section h2, .progress-section h5, .progress-section h6, .progress-section p {
            color: white !important;
        }
        
        /* 改善任务项目的可读性 */
        .task-item {
            border: 1px solid #dee2e6;
        }
        
        .task-item h6 {
            color: #2c3e50;
            font-weight: 600;
        }
        
        /* 改善表单标签的可读性 */
        .filter-section .form-label {
            color: #2c3e50;
            font-weight: 600;
        }
        
        /* 强制任务详情内所有文字都可读 */
        .task-details-section * {
            color: #2c3e50 !important;
        }
        
        .task-details-section .text-muted {
            color: #6c757d !important;
        }
        
        /* badge样式优化 - 允许内联样式覆盖 */
        .badge {
            border-radius: 6px;
            font-size: 0.75rem;
            padding: 4px 8px;
        }
        
        /* 只对没有内联样式的badge应用默认白色文字 */
        .badge:not([style*="color"]) {
            color: white;
        }
        
        /* 流程图样式 */
        .process-flow {
            padding: 20px;
            overflow-x: auto;
        }
        
        .process-flow-container {
            min-height: 120px;
            padding: 10px 0;
        }
        
        .process-step {
            display: inline-block;
            position: relative;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            margin: 8px 5px;
            color: white;
            text-align: center;
            min-width: 160px;
            max-width: 180px;
            transition: all 0.3s ease;
            cursor: pointer;
            backdrop-filter: blur(5px);
        }
        
        .process-step.my-step {
            background: rgba(255, 193, 7, 0.4);
            border-color: #ffc107;
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.4);
            transform: scale(1.05);
        }
        
        .process-step.completed {
            background: rgba(25, 135, 84, 0.3);
            border-color: #198754;
        }
        
        .process-step.current {
            background: rgba(13, 110, 253, 0.3);
            border-color: #0d6efd;
            animation: pulse 2s infinite;
        }
        
        .process-step.pending {
            background: rgba(108, 117, 125, 0.3);
            border-color: #6c757d;
        }
        
        .process-step:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
            background: rgba(255, 255, 255, 0.2);
        }
        
        .process-step.my-step:hover {
            transform: translateY(-3px) scale(1.08);
        }
        
        .process-arrow {
            display: inline-flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.4rem;
            margin: 0 8px;
            transition: all 0.3s ease;
        }
        
        .process-arrow:hover {
            color: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }
        
        .process-parallel {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            margin: 8px 5px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .process-parallel .process-step {
            margin: 4px 0;
            min-width: 180px;
        }
        
        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .step-status {
            font-size: 0.75rem;
            opacity: 0.8;
        }
        
        .step-icon {
            font-size: 1.2rem;
            margin-bottom: 5px;
            display: block;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7); }
            70% { box-shadow: 0 0 0 15px rgba(13, 110, 253, 0); }
            100% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0); }
        }
        
        /* 待办任务样式 */
        .pending-task-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
            border-left: 3px solid #ffc107;
        }
        
        .task-priority {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7rem;
            margin-left: 5px;
        }
        
        .priority-high {
            background: #ffebeb;
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }
        
        .priority-medium {
            background: #fff4e6;
            color: #f57c00;
            border: 1px solid rgba(245, 124, 0, 0.2);
        }
        
        .priority-low {
            background: #e8f5e8;
            color: #198754;
            border: 1px solid rgba(25, 135, 84, 0.2);
        }
        
        /* 批量标记问题模态框样式 */
        #batchProblemTagModal .alert-info {
            border-left: 4px solid #0dcaf0;
            background: linear-gradient(135deg, #e7f6ff 0%, #f0f9ff 100%);
        }
        
        #batchProblemTagModal .form-select {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }
        
        #batchProblemTagModal .form-select:focus {
            border-color: #ffc107;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }
        
        #batchProblemTagModal .form-check-input:checked {
            background-color: #ffc107;
            border-color: #ffc107;
        }
        
        #batch-selected-tasks {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
        }
        
        #batch-selected-tasks::-webkit-scrollbar {
            width: 6px;
        }
        
        #batch-selected-tasks::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        #batch-selected-tasks::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        #batch-selected-tasks::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .progress-section {
                padding: 12px 0;
            }
            
            .task-details-section {
                padding: 15px;
            }
            
            .process-flow {
                padding: 8px;
            }
            
            .process-flow-container {
                flex-direction: column;
                align-items: center;
                min-height: auto;
            }
            
            .process-step {
                min-width: 280px;
                max-width: 300px;
                padding: 15px 20px;
                margin: 8px 0;
                font-size: 0.9rem;
            }
            
            .process-parallel {
                width: 100%;
                margin: 10px 0;
                padding: 15px;
            }
            
            .process-parallel .process-step {
                min-width: 250px;
                margin: 8px 0;
            }
            
            .process-arrow {
                font-size: 1.5rem;
                margin: 8px 0;
                transform: rotate(90deg);
            }
            
            .step-title {
                font-size: 0.9rem;
                font-weight: 600;
            }
            
            .step-status {
                font-size: 0.8rem;
            }
            
            .step-icon {
                font-size: 1.4rem;
            }
            
            /* 步骤详情弹窗在移动端的优化 */
            .step-detail-popup {
                max-width: 95% !important;
                width: 95% !important;
                max-height: 90vh !important;
            }
        }
        
        @media (max-width: 480px) {
            .process-step {
                min-width: 260px;
                padding: 12px 16px;
                font-size: 0.85rem;
            }
            
            .process-parallel .process-step {
                min-width: 230px;
            }
            
            .step-title {
                font-size: 0.85rem;
            }
            
            .step-status {
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-kanban text-primary"></i>
                OCR校验任务监控看板
            </a>
            <div class="navbar-nav ms-auto">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshAllData()" id="refresh-btn">
                    <i class="bi bi-arrow-clockwise"></i> 刷新数据
                </button>
                <span class="navbar-text ms-3">
                    <i class="bi bi-clock"></i>
                    <span id="current-time"></span>
                </span>
            </div>
        </div>
    </nav>

    <!-- 流程位置区域 -->
    <div class="progress-section">
        <div class="container">
            <!-- 流程图区域 -->
            <div class="row">
                <div class="col-12">
                    <div class="progress-card">
                        <div class="process-flow" id="process-flow">
                            <!-- 流程步骤将通过JavaScript动态生成 -->
                            <div class="text-center py-2">
                                <i class="bi bi-hourglass-split" style="font-size: 1.5rem; color: rgba(255,255,255,0.5);"></i>
                                <p class="mt-1 mb-0" style="color: rgba(255,255,255,0.7); font-size: 0.9rem;">正在加载流程信息...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务详情区域 -->
    <div class="container">
        <!-- 过滤器 -->
        <div class="filter-section">
            <div class="row align-items-center">
                                <div class="col-md-4">
                    <label class="form-label fw-bold">状态筛选</label>
                    <select class="form-select" id="status-filter" onchange="filterTasks()">
                        <option value="">全部状态</option>
                        <option value="未开始">未开始</option>
                        <option value="进行中">进行中</option>
                        <option value="已完成">已完成</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label fw-bold">搜索任务</label>
                    <input type="text" class="form-control" id="search-input" 
                           placeholder="搜索任务名称或内容..." onkeyup="filterTasks()">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-outline-secondary d-block w-100" onclick="clearFilters()">
                        <i class="bi bi-funnel"></i> 清除筛选
                    </button>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="task-details-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4>
                    <i class="bi bi-list-task"></i>
                    任务详情
                    <span class="badge" style="background-color: #e7f3ff; color: #0d6efd; border: 1px solid rgba(13, 110, 253, 0.2);" id="task-count">0</span>
                </h4>
                <div class="btn-group">
                    <button class="btn btn-outline-success btn-sm" onclick="batchUpdateStatus('已完成')" id="batch-complete-btn" disabled>
                        <i class="bi bi-check-circle"></i> 批量完成
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="showBatchProblemTagModal()" id="batch-tag-btn" disabled>
                        <i class="bi bi-tags"></i> 批量标记问题
                    </button>
                </div>
            </div>
            
            <!-- 任务项目列表 -->
            <div id="task-list">
                <div class="text-center text-muted py-5">
                    <i class="bi bi-hourglass-split display-1"></i>
                    <p class="mt-3">正在加载任务数据...</p>
                </div>
            </div>
        </div>

        <!-- 数据更新状态 -->
        <div class="alert alert-info" role="alert">
            <i class="bi bi-info-circle"></i>
            <span id="update-status">数据每5分钟自动更新一次</span>
            <small class="ms-3">上次更新: <span id="last-update-time">--:--:--</span></small>
        </div>
    </div>

    <!-- 任务详情Modal -->
    <div class="modal fade" id="taskDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">任务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="task-detail-content">
                    <!-- 任务详情内容 -->
                </div>
                <div class="modal-footer">
                    <div class="container-fluid">
                        <!-- 第一部分：变更状态 -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <h6 class="mb-2"><i class="bi bi-arrow-repeat"></i> 变更状态</h6>
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="updateTaskStatus(currentTaskId, '未开始')">
                                        <i class="bi bi-pause-circle"></i> 未开始
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="updateTaskStatus(currentTaskId, '进行中')">
                                        <i class="bi bi-play-circle"></i> 进行中
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="updateTaskStatus(currentTaskId, '已完成')">
                                        <i class="bi bi-check-circle"></i> 已完成
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 第二部分：添加问题判断结果 -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <h6 class="mb-2"><i class="bi bi-tags"></i> 添加问题判断结果</h6>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="input-group">
                                            <select class="form-select" id="problem-type-select">
                                                <option value="">选择问题类型</option>
                                                <!-- 选项将由JavaScript动态生成 -->
                                            </select>
                                            <button class="btn btn-primary" type="button" onclick="addProblemTag()">
                                                <i class="bi bi-plus-circle"></i> 添加
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-warning w-100" onclick="clearAllTags()">
                                            <i class="bi bi-trash"></i> 清除标签
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 标签显示区域 -->
                                <div id="problem-tags-display" class="mt-2 p-2 bg-light rounded min-height-40">
                                    <small class="text-muted">暂无标签</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="row">
                            <div class="col-12 text-end">
                                <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">
                                    <i class="bi bi-x-circle"></i> 关闭
                                </button>
                                <button type="button" class="btn btn-success" onclick="saveTaskChanges()">
                                    <i class="bi bi-save"></i> 保存更改
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知Modal -->
    <div class="modal fade" id="notificationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">发送通知</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="notificationForm">
                        <div class="mb-3">
                            <label class="form-label">接收人：</label>
                            <input type="text" class="form-control" id="recipient" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">消息内容：</label>
                            <textarea class="form-control" id="message" rows="3" placeholder="请输入要发送的消息..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmSendNotification()">发送</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量标记问题Modal -->
    <div class="modal fade" id="batchProblemTagModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-tags"></i>
                        批量标记问题原因
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        即将为 <span id="batch-tag-count">0</span> 个选中的任务添加问题标签
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">选择问题类型：</label>
                        <select class="form-select" id="batch-problem-type-select">
                            <option value="">请选择问题类型</option>
                            <!-- 选项将由JavaScript动态生成 -->
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="batch-replace-tags">
                            <label class="form-check-label" for="batch-replace-tags">
                                替换现有标签（如不勾选则追加到现有标签）
                            </label>
                        </div>
                    </div>
                    
                    <div class="bg-light p-3 rounded">
                        <h6 class="mb-2">选中的任务：</h6>
                        <div id="batch-selected-tasks" class="small text-muted">
                            <!-- 选中的任务列表将在这里显示 -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> 取消
                    </button>
                    <button type="button" class="btn btn-warning" onclick="confirmBatchProblemTag()">
                        <i class="bi bi-tags"></i> 批量标记
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 传递后端变量到JavaScript -->
    <script>
        // 个性化页面配置
        window.personalPageConfig = {
            isPersonalPage: {{ 'true' if person_code else 'false' }},
            personCode: '{{ person_code or '' }}',
            personName: '{{ person_name or default_person_filter or '' }}',
            defaultPersonFilter: '{{ default_person_filter or '' }}'
        };
        
        // 调试输出配置信息
        console.log('页面配置传递:', window.personalPageConfig);
    </script>
    
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html> 