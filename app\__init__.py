#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
App模块 - 应用层
包含Flask Web应用、API路由、视图控制器等应用层组件
"""

# Web应用主模块
from .web_app import create_app, get_monitor_service, get_config_loader

# API路由
from .api_routes import register_api_routes

# 视图路由
from .views import register_view_routes


__all__ = [
    # Web应用
    'create_app',
    'get_monitor_service',
    'get_config_loader',
    
    # 路由注册
    'register_api_routes',
    'register_view_routes'
] 