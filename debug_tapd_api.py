#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试TAPD API返回数据的脚本
"""

import asyncio
import logging
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils import ConfigLoader, TAPDClient

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def debug_tapd_api():
    """调试TAPD API"""
    print("🔍 调试TAPD API返回数据")
    
    # 初始化客户端
    config_loader = ConfigLoader()
    tapd_client = TAPDClient(config_loader)
    workspace_id = config_loader.get_workspace_id()
    
    # 测试任务ID
    task_id = "1138183800001000919"
    story_id = "1138183800001000907"
    
    print(f"📝 测试任务ID: {task_id}")
    print(f"📝 测试需求ID: {story_id}")
    
    # 1. 获取单个任务的原始数据
    print(f"\n🔍 获取单个任务的原始数据...")
    result = await tapd_client.get_task(workspace_id, task_id)
    print(f"原始返回数据:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 2. 获取需求下所有任务的原始数据
    print(f"\n🔍 获取需求下所有任务的原始数据...")
    result = await tapd_client.get_tasks(workspace_id, story_id=story_id)
    print(f"原始返回数据:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 3. 测试创建一个新任务
    print(f"\n🔍 测试创建新任务...")
    test_task_data = {
        "name": "测试任务-调试用",
        "description": "这是一个用于调试的测试任务",
        "story_id": story_id,
        "owner": "刘青昀",
        "priority": "中"
    }
    
    result = await tapd_client.create_task(
        workspace_id=workspace_id,
        **test_task_data
    )
    print(f"创建任务返回数据:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if result.get("status") == 1:
        # 提取新创建的任务ID
        data = result.get("data", {})
        new_task_id = None
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, dict) and value.get("id"):
                    new_task_id = str(value["id"])
                    break
        
        if new_task_id:
            print(f"✅ 新任务创建成功，ID: {new_task_id}")
            
            # 立即获取新创建的任务详情
            print(f"\n🔍 获取新创建任务的详情...")
            result = await tapd_client.get_task(workspace_id, new_task_id)
            print(f"新任务详情:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print("❌ 无法提取新任务ID")
    else:
        print("❌ 任务创建失败")
    
    print("\n✅ 调试完成")

if __name__ == '__main__':
    asyncio.run(debug_tapd_api())
