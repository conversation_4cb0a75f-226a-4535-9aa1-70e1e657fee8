#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务状态监控服务 - Core层
事件驱动的任务状态管理，根据用户操作触发后续任务创建和批次完成检查
"""

import logging
import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set

from utils import ConfigLoader
from .tapd_service import TAPDService
from .ocr_service import OCRService


class TaskStatusMonitor:
    """任务状态监控服务（事件驱动）"""
    
    def __init__(self, config_loader: Optional[ConfigLoader] = None):
        """
        初始化任务状态监控服务
        
        Args:
            config_loader: 配置加载器实例
        """
        self.logger = logging.getLogger(__name__)
        self.config_loader = config_loader or ConfigLoader()
        self.tapd_service = TAPDService(self.config_loader)
        self.ocr_service = OCRService(self.config_loader)
        
        # 获取配置
        self.ocr_config = self.config_loader.get_ocr_config()
        self.followup_rules = self.ocr_config.get("followup_rules", [])
        self.responsible_persons = self.config_loader.get_responsible_persons()
        
        # 监控状态
        self.is_monitoring = False
        
        # 任务状态缓存
        self.task_status_cache = {}  # task_id -> {"status": str, "labels": list, "last_check": datetime}
        self.processed_tasks = set()  # 已处理过的任务ID集合
        self.batch_tasks = {}  # 批次任务映射 story_id -> [task_ids]
        
        self.logger.info("任务状态监控服务初始化完成（事件驱动模式）")
    
    def start_monitoring(self) -> bool:
        """
        启动任务状态监控（事件驱动模式）
        
        Returns:
            是否启动成功
        """
        self.logger.info("任务状态监控已启动（事件驱动模式，无定期检查）")
        self.is_monitoring = True
        return True
    
    def stop_monitoring(self):
        """停止任务状态监控"""
        self.logger.info("任务状态监控已停止")
        self.is_monitoring = False
    
    async def handle_task_tag_added(self, task_id: str, labels: List[str]):
        """
        处理任务标签添加事件（供API调用）

        Args:
            task_id: 任务ID
            labels: 新添加的标签列表
        """
        try:
            # 获取任务详情
            task_details = await self.tapd_service.get_task_details(task_id)
            if not task_details.get("success"):
                self.logger.error(f"获取任务 {task_id} 详情失败")
                return

            raw_task_info = task_details.get("task", {})

            # 格式化任务状态
            from utils import format_status
            raw_status = raw_task_info.get('status', '')
            formatted_status = format_status(raw_status)

            self.logger.info(f"任务 {task_id} 原始状态: '{raw_status}' -> 格式化状态: '{formatted_status}'")

            # 创建格式化的任务信息
            task_info = raw_task_info.copy()
            task_info['status'] = formatted_status

            # 确保story_id正确设置
            story_id = task_info.get('story_id')
            if not story_id or story_id == "0":
                self.logger.warning(f"任务 {task_id} 的story_id为空或为0，尝试从其他方式获取")
                # 可以尝试从任务名称或其他方式推断story_id
                # 这里暂时记录警告，但仍然继续处理

            self.logger.info(f"任务 {task_id} 的story_id: {story_id}")

            # 直接处理添加标签的任务，不再判断任务状态
            self.logger.info(f"检测到任务 {task_id} 添加标签: {labels}，直接创建后续任务")
            await self._handle_task_with_labels(task_info, labels)

        except Exception as e:
            self.logger.error(f"处理任务标签添加事件失败: {e}")
    
    async def handle_task_status_changed(self, task_id: str, new_status: str, story_id: str = None):
        """
        处理任务状态变更事件（供API调用）
        
        Args:
            task_id: 任务ID
            new_status: 新状态
            story_id: 需求ID（可选）
        """
        try:
            self.logger.info(f"任务 {task_id} 状态变更为: {new_status}")
            
            # 如果任务完成且有story_id，检查批次完成状态
            if new_status == '已完成' and story_id:
                await self._check_single_batch_completion(story_id, [])
            
        except Exception as e:
            self.logger.error(f"处理任务状态变更失败: {e}")
    
    async def _handle_task_with_labels(self, task: Dict[str, Any], labels: List[str]):
        """
        处理有标签的任务（不考虑任务状态）

        Args:
            task: 任务信息
            labels: 任务标签列表
        """
        try:
            task_id = task.get('id')
            task_name = task.get('name', '')
            story_id = task.get('story_id')

            self.logger.info(f"处理带标签任务: {task_name} ({task_id}), 标签: {labels}")

            # 根据标签创建后续任务
            for label in labels:
                await self._create_followup_task_for_label(task, label)

            # 记录批次信息
            if story_id:
                if story_id not in self.batch_tasks:
                    self.batch_tasks[story_id] = []
                if task_id not in self.batch_tasks[story_id]:
                    self.batch_tasks[story_id].append(task_id)

        except Exception as e:
            self.logger.error(f"处理带标签任务失败: {e}")

    async def _create_followup_task_for_label(self, original_task: Dict[str, Any], label: str):
        """
        根据标签创建后续任务
        
        Args:
            original_task: 原始任务信息
            label: 问题标签
        """
        try:
            # 查找匹配的后续任务规则
            matching_rule = None
            for rule in self.followup_rules:
                if rule.get('label') == label:
                    matching_rule = rule
                    break
            
            if not matching_rule:
                self.logger.warning(f"未找到标签 '{label}' 对应的后续任务规则")
                return
            
            # 获取story_id，如果原始任务的story_id不正确，尝试通过其他方式获取
            story_id = original_task.get('story_id')
            original_task_id = original_task.get('id')

            if not story_id or story_id == "0":
                self.logger.warning(f"原始任务 {original_task_id} 的story_id无效: {story_id}，尝试获取正确的story_id")

                # 方法1: 尝试从已知的批次任务中推断story_id
                for batch_story_id, task_ids in self.batch_tasks.items():
                    if original_task_id in task_ids:
                        story_id = batch_story_id
                        self.logger.info(f"从批次任务中推断出story_id: {story_id}")
                        break

                # 方法2: 如果还是没有找到，通过查找同名任务来推断
                if not story_id or story_id == "0":
                    story_id = await self._find_story_id_by_task_pattern(original_task.get('name', ''))
                    if story_id:
                        self.logger.info(f"通过任务模式推断出story_id: {story_id}")

                # 如果还是没有找到，跳过创建
                if not story_id or story_id == "0":
                    self.logger.error(f"无法确定原始任务 {original_task_id} 的story_id，跳过后续任务创建")
                    return

            new_task_title = matching_rule.get('new_task_title', f'{label}处理任务')
            owner = matching_rule.get('owner', '')

            self.logger.info(f"创建后续任务: {new_task_title}, story_id: {story_id}, 负责人: {owner}")
            
            task_description = f"""
<h3>后续处理任务</h3>
<p><strong>原始任务:</strong> {original_task.get('name', '')}</p>
<p><strong>问题标签:</strong> {label}</p>
<p><strong>创建时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>处理说明:</strong> 请根据问题标签进行相应处理</p>
"""
            
            task_data = {
                "name": new_task_title,
                "description": task_description,
                "story_id": story_id,
                "owner": owner,
                "priority": "中"
            }
            
            result = await self.tapd_service.create_ocr_task(task_data)
            
            if result.get("success"):
                new_task_id = result.get("task_id")
                self.logger.info(f"成功创建后续任务: {new_task_title} ({new_task_id}), 负责人: {owner}")
                
                # 创建对应的通知任务（仅为实际负责人）
                await self._create_simple_notification_task(story_id, owner, label)
            else:
                self.logger.error(f"创建后续任务失败: {result.get('message')}")
                
        except Exception as e:
            self.logger.error(f"创建标签 '{label}' 的后续任务失败: {e}")

    async def _find_story_id_by_task_pattern(self, task_name: str) -> Optional[str]:
        """
        通过任务名称模式查找对应的story_id

        Args:
            task_name: 任务名称

        Returns:
            story_id或None
        """
        try:
            # 如果是准确性或完备性问题任务，尝试查找同类型的其他任务
            if '准确性问题' in task_name or '完备性问题' in task_name:
                # 获取所有负责人的任务，查找同类型任务
                responsible_persons = self.config_loader.get_responsible_persons()

                for person in responsible_persons.values():
                    tasks = await self.tapd_service.get_person_tasks(person)
                    for task in tasks:
                        task_task_name = task.get('name', '')
                        task_story_id = task.get('story_id', '')

                        # 如果找到同类型的任务且有有效的story_id
                        if (('准确性问题' in task_task_name or '完备性问题' in task_task_name) and
                            task_story_id and task_story_id != "0"):
                            self.logger.info(f"通过任务模式找到story_id: {task_story_id} (参考任务: {task_task_name})")
                            return task_story_id

            return None

        except Exception as e:
            self.logger.error(f"通过任务模式查找story_id失败: {e}")
            return None

    async def _check_single_batch_completion(self, story_id: str, task_ids: List[str] = None):
        """
        检查单个批次的完成状态
        
        Args:
            story_id: 需求ID
            task_ids: 任务ID列表（可选，如果不提供将自动获取）
        """
        try:
            self.logger.info(f"开始检查批次 {story_id} 的完成状态")

            # 获取该批次的所有任务状态
            all_completed = True
            has_followup_tasks = False

            # 获取需求下的所有任务
            story_tasks = await self.tapd_service.get_story_tasks(story_id)
            self.logger.info(f"批次 {story_id} 共有 {len(story_tasks)} 个任务")
            
            original_tasks = []
            followup_tasks = []

            for task in story_tasks:
                task_id = task.get('id')
                task_name = task.get('name', '')
                task_status = task.get('status')

                self.logger.debug(f"检查任务: {task_name} (状态: {task_status})")

                # 检查是否为原始任务（准确性问题或完备性问题）
                if ('准确性问题' in task_name or '完备性问题' in task_name or
                    task_name.startswith('准确性问题-') or task_name.startswith('完备性问题-')):
                    original_tasks.append({"name": task_name, "status": task_status})
                    if task_status != '已完成':
                        all_completed = False
                        self.logger.info(f"原始任务未完成: {task_name} (状态: {task_status})")

                # 检查是否有后续任务（动态从配置获取）
                followup_task_titles = [rule.get('new_task_title', '') for rule in self.followup_rules]
                if any(title in task_name for title in followup_task_titles if title):
                    has_followup_tasks = True
                    followup_tasks.append({"name": task_name, "status": task_status})
                    if task_status != '已完成':
                        all_completed = False
                        self.logger.info(f"后续任务未完成: {task_name} (状态: {task_status})")

            self.logger.info(f"批次 {story_id} 任务统计: 原始任务 {len(original_tasks)} 个, 后续任务 {len(followup_tasks)} 个")
            
            # 检查重新OCR任务是否已存在
            reocr_exists = False
            for task in story_tasks:
                task_name = task.get('name', '')
                if '重新OCR' in task_name:
                    self.logger.info(f"批次 {story_id} 已存在重新OCR任务: {task_name}")
                    reocr_exists = True
                    break
            
            # 如果重新OCR任务已存在，从监控列表中移除该批次
            if reocr_exists:
                if story_id in self.batch_tasks:
                    del self.batch_tasks[story_id]
                return
            
            # 如果所有后续任务都完成，创建重新OCR任务
            if all_completed and has_followup_tasks:
                self.logger.info(f"批次 {story_id} 所有任务已完成，创建重新OCR任务")
                self.logger.info(f"  - 原始任务: {[t['name'] for t in original_tasks]}")
                self.logger.info(f"  - 后续任务: {[t['name'] for t in followup_tasks]}")
                await self._create_reocr_task(story_id)
                # 从监控列表中移除该批次
                if story_id in self.batch_tasks:
                    del self.batch_tasks[story_id]
            elif not has_followup_tasks:
                self.logger.info(f"批次 {story_id} 没有后续任务，无需创建重新OCR任务")
                self.logger.info(f"  - 原始任务: {[t['name'] for t in original_tasks]}")
            else:
                self.logger.info(f"批次 {story_id} 还有未完成的任务，继续监控")
                self.logger.info(f"  - 未完成的原始任务: {[t['name'] for t in original_tasks if t['status'] != '已完成']}")
                self.logger.info(f"  - 未完成的后续任务: {[t['name'] for t in followup_tasks if t['status'] != '已完成']}")
            
        except Exception as e:
            self.logger.error(f"检查批次 {story_id} 完成状态失败: {e}")
    
    async def _create_reocr_task(self, story_id: str):
        """
        创建重新OCR任务
        
        Args:
            story_id: 需求ID
        """
        try:
            # 获取OCR校验角色名称
            ocr_role_name = self.config_loader.get_role_name_by_role('ocr_validation')
            
            task_description = f"""
<h3>重新OCR处理任务</h3>
<p><strong>需求ID:</strong> {story_id}</p>
<p><strong>创建时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>处理说明:</strong> 本批次所有问题已处理完成，请进行重新OCR处理</p>
"""
            
            task_data = {
                "name": "重新OCR处理",
                "description": task_description,
                "story_id": story_id,
                "owner": ocr_role_name,
                "priority": "高"
            }
            
            result = await self.tapd_service.create_ocr_task(task_data)
            
            if result.get("success"):
                new_task_id = result.get("task_id")
                self.logger.info(f"成功创建重新OCR任务: {new_task_id}, 负责人: {ocr_role_name}")
            else:
                self.logger.error(f"创建重新OCR任务失败: {result.get('message')}")
                
        except Exception as e:
            self.logger.error(f"创建重新OCR任务失败: {e}")
    
    async def _create_simple_notification_task(self, story_id: str, role_name: str, label: str):
        """
        创建通知任务
        
        Args:
            story_id: 需求ID
            role_name: 角色名称（如"准确性环节人员"）
            label: 任务标签
        """
        try:
            local_ip = self.config_loader.get_local_ip()
            host, port = self.config_loader.get_web_host_port()
            
            # 获取实际负责人姓名
            actual_person = self.config_loader.get_actual_person_by_role(role_name.replace("环节人员", ""))
            if not actual_person:
                actual_person = role_name
            
            task_description = f"""
<h3>OCR异常处理提醒</h3>
<p><strong>需求ID:</strong> {story_id}</p>
<p><strong>创建时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>处理说明:</strong> 您有新的OCR异常任务需处理</p>
<p><strong>查看地址:</strong> <a href="http://{local_ip}:{port}/dashboard/{actual_person}" target="_blank">点击进入个人看板查看</a></p>
"""
            
            # 使用TAPD标准状态值
            from utils import format_status_for_tapd
            completed_status = format_status_for_tapd("已完成")
            
            self.logger.info(f"创建通知任务，负责人: {actual_person}, 状态: {completed_status}")
            
            task_data = {
                "name": "您有新的OCR异常任务需处理",
                "description": task_description,
                "story_id": story_id,
                "owner": actual_person,
                "priority": "高",
                "status": completed_status  # 通知任务自动完成
            }
            
            result = await self.tapd_service.create_ocr_task(task_data)
            
            if result.get("success"):
                new_task_id = result.get("task_id")
                self.logger.info(f"成功创建通知任务（已自动完成）: {new_task_id}, 负责人: {actual_person}")
            else:
                self.logger.error(f"创建通知任务失败: {result.get('message')}")
                
        except Exception as e:
            self.logger.error(f"创建通知任务失败: {e}")
    
    def get_monitor_status(self) -> Dict[str, Any]:
        """
        获取监控状态信息
        
        Returns:
            监控状态信息
        """
        return {
            "is_monitoring": self.is_monitoring,
            "mode": "event_driven",
            "task_count": len(self.task_status_cache),
            "processed_count": len(self.processed_tasks),
            "batch_count": len(self.batch_tasks),
            "followup_rules_count": len(self.followup_rules),
            "last_check": datetime.now().isoformat()
        }
